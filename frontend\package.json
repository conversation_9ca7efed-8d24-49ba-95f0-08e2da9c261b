{"name": "ghl-frontend", "version": "0.1.0", "proxy": "https://gonano.flowcodes.in", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^6.4.6", "@mui/material": "^6.4.6", "@mui/x-charts": "^7.27.1", "@mui/x-data-grid": "^7.27.1", "@mui/x-date-pickers": "^7.27.1", "@mui/x-tree-view": "^7.26.0", "@react-spring/web": "^9.7.5", "axios": "^1.8.4", "cra-template": "1.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-redux": "^9.2.0", "react-router": "6.15.0", "react-router-dom": "6.15.0", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "recharts": "^2.15.1", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "web-vitals": "^4.2.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.17"}}
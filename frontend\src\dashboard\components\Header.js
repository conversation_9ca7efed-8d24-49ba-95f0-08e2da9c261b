// import * as React from 'react';
// import Stack from '@mui/material/Stack';
// import NotificationsRoundedIcon from '@mui/icons-material/NotificationsRounded';
// import CustomDatePicker from './CustomDatePicker';
// import NavbarBreadcrumbs from './NavbarBreadcrumbs';
// import MenuButton from './MenuButton';
// import ColorModeIconDropdown from '../../shared-theme/ColorModeIconDropdown';

// import Search from './Search';

// export default function Header({title}) {
//   console.log("contact",title);
  
//   return (
//     <Stack
//       direction="row"
//       sx={{
//         display: { xs: 'none', md: 'flex' },
//         width: '100%',
//         alignItems: { xs: 'flex-start', md: 'center' },
//         justifyContent: 'space-between',
//         maxWidth: { sm: '100%', md: '1700px' },
//         pt: 1.5,
//         padding:'1rem',
//         boxShadow:'0 2px 3px rgba(0,0,0,.11)'
//       }}
//       spacing={2}
//     >
//       <NavbarBreadcrumbs title = {title}/>
//       <Stack direction="row" sx={{ gap: 1 }}>
//         {/* <Search width='25ch'/> */}
//         <CustomDatePicker />
//         <MenuButton showBadge aria-label="Open notifications">
//           <NotificationsRoundedIcon />
//         </MenuButton>
//         <ColorModeIconDropdown />
//       </Stack>
//     </Stack>
//   );
// }


import * as React from 'react';
import Stack from '@mui/material/Stack';
import NotificationsRoundedIcon from '@mui/icons-material/NotificationsRounded';
import CustomDatePicker from './CustomDatePicker';
import NavbarBreadcrumbs from './NavbarBreadcrumbs';
import MenuButton from './MenuButton';
import ColorModeIconDropdown from '../../shared-theme/ColorModeIconDropdown';

import Search from './Search';

export default function Header({title}) {
  // console.log("contact",title);
  
  return ( 
    <Stack
      direction="row"
      sx={{
        display: { xs: 'none', md: 'flex' },
        width: '100%',
        alignItems: { xs: 'flex-start', md: 'center' },
        justifyContent: 'space-between',
        maxWidth: { sm: '100%', md: '1700px' },
        pt: 1.5,
        padding:'1rem',
        backgroundColor: (theme) => theme.palette.background.paper,
        boxShadow: (theme) => theme.palette.mode === 'dark'
          ? '0 2px 3px rgba(0,0,0,.3)'
          : '0 2px 3px rgba(0,0,0,.11)'
      }}
      spacing={2}
    >
      <NavbarBreadcrumbs title = {title}/>
      <Stack direction="row" sx={{ gap: 1 }}>
        {/* <Search width='25ch'/> */}
        <CustomDatePicker />
        <MenuButton showBadge aria-label="Open notifications">
          <NotificationsRoundedIcon />
        </MenuButton>
        <ColorModeIconDropdown />
      </Stack>
    </Stack>
  );
}

// 📁 server.js (Backend with Express)
require("dotenv").config();
const express = require("express");
const cors = require("cors");
const connectDB = require('./config/db');
const { createGhlDataTable } = require('./models/GhlData');

const app = express();

// ✅ CORS: Allow all origins (only for development/testing)
const corsOptions = {
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}; 

app.use(cors(corsOptions));
app.use(express.json());

// 🔗 Connect to MySQL and initialize tables
const initializeDatabase = async () => {
  try {
    const connection = await connectDB();
    await createGhlDataTable(connection);
    // Make the connection available globally
    app.locals.db = connection;
  } catch (error) {
    console.error('Database initialization error:', error);
    process.exit(1);
  }
};

initializeDatabase();

// 🔀 Routes
const authRoutes = require("./routes/authRoutes");
const locationsRoutes = require("./routes/locations");
const salesdataRoutes = require("./routes/growthRate");
const pipelinesRoutes = require("./routes/pipelines");
const calendarRoutes = require("./routes/Calendar");
const CalendarServiceRoutes = require("./routes/CalendarService");
const usersRoutes = require("./routes/userRoutes");
const contactsRoutes = require("./routes/contacts");
const conversationRoutes = require("./routes/conversation");
const tasksroutes = require("./routes/tasks");
const monthlyTransactions = require("./routes/monthlyRevenue");
const salesRoutes = require("./routes/sales");
const opportunitiesRoutes = require("./routes/opportunities");
const subscriptionsRoutes = require("./routes/subscriptions");
const customersRoutes = require("./routes/customer");
const funnelRoutes = require("./routes/funnel");
const signInRoutes = require("./routes/signin");
const signUpRoutes = require("./routes/signup");
const testingAPI = require("./routes/testingAPI");

// ✅ Location token V2 API route
app.post("/api/get-location-token", async (req, res) => {
  const { companyId, locationId } = req.body;

  if (!companyId || !locationId) {
    return res
      .status(400)
      .json({ error: "companyId and locationId are required" });
  }

  console.log("🔐 Requesting location token for:");
  console.log("👉 Company ID:", companyId);
  console.log("👉 Location ID:", locationId);

  try {
    const response = await fetch(
      "https://services.leadconnectorhq.com/oauth/locationToken",
      {
        method: 'POST',
        headers: {
          'Version': '2021-07-28',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        body: new URLSearchParams({ companyId, locationId })
      }
    );

    const data = await response.json();
    return res.json(data);
  } catch (err) {
    console.error(
      "❌ Error fetching location token:",
      err.message
    );
    return res
      .status(500)
      .json({ error: err.message || "Server error" });
  }
});

// 🧩 All routes
app.use("/api/auth", authRoutes);
app.use("/api", locationsRoutes);
app.use("/api", salesdataRoutes);
app.use("/api", pipelinesRoutes);
app.use("/api", calendarRoutes);
app.use("/api", CalendarServiceRoutes);
app.use("/api", usersRoutes);
app.use("/api", contactsRoutes);
app.use("/api", tasksroutes);
app.use("/api", monthlyTransactions);
app.use("/api", salesRoutes);
app.use("/api", opportunitiesRoutes);
app.use("/api", subscriptionsRoutes);
app.use("/api", customersRoutes);
app.use("/api", funnelRoutes);
app.use("/api", signInRoutes);
app.use("/api", signUpRoutes);
app.use("/api/test", testingAPI);
app.use("/api/conversation", conversationRoutes);

// 🟢 Start server
const PORT = process.env.PORT || 5000;
const HOST = '0.0.0.0';

app.listen(PORT, HOST, () => {
  console.log(`🚀 Server running at http://${HOST}:${PORT}`);
  console.log(`🌐 Local URL: http://localhost:${PORT}`);
  console.log(`🌐 Network URL: http://${getLocalIP()}:${PORT}`);
  console.log(`🌐 Network URL: http://${getLocalIP()}:${PORT}`);
});

// 🔍 Local IP detection utility
function getLocalIP() {
  const { networkInterfaces } = require('os');
  const nets = networkInterfaces();
  const results = {};

  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      if (net.family === 'IPv4' && !net.internal) {
        if (!results[name]) {
          results[name] = [];
        }
        results[name].push(net.address);
      }
    }
  }

  for (const name of Object.keys(results)) {
    if (results[name].length > 0) {
      return results[name][0];
    }
  }
  return 'localhost';
}


// const express = require("express");
// const axios = require("axios");
// const cors = require("cors");

// const app = express();
// const PORT = 5000;

// // Middleware
// app.use(cors());
// app.use(express.json());

// const CLIENT_ID = "67990c955eefdf2ff5493cfd-m9id3rb8";
// const CLIENT_SECRET = "5046f197-49b1-4031-a5ae-88117a035caf";
// const REDIRECT_URI = "http://localhost:3000/callback";

// // Step 1: Handle redirect from GHL (user login)
// app.get("/callback", async (req, res) => {
//   const { code } = req.query;

//   if (!code) {
//     return res.status(400).send("Authorization code missing");
//   }

//   try {
//     const tokenRes = await axios.post(
//       "https://services.leadconnectorhq.com/oauth/token",
//       {
//         client_id: CLIENT_ID,
//         client_secret: CLIENT_SECRET,
//         grant_type: "authorization_code",
//         code,
//         redirect_uri: REDIRECT_URI,
//       },
//       {
//         headers: {
//           "Content-Type": "application/json",
//         },
//       }
//     );

//     console.log("✅ Access Token Response:", tokenRes.data);

//     res.json({
//       message: "Token received successfully",
//       ...tokenRes.data,
//     });
//   } catch (error) {
//     console.error("❌ Error fetching token:", error.response?.data || error.message);
//     res.status(500).json({
//       message: "Failed to exchange code for token",
//       error: error.response?.data || error.message,
//     });
//   }
// });

// app.get('/api/auth/oauth/callback', async (req, res) => {
//   const { code } = req.query;

//   try {
//     const tokenResponse = await axios.post('https://services.leadconnectorhq.com/oauth/token', null, {
//       params: {
//         grant_type: 'authorization_code',
//         code,
//         redirect_uri: 'http://localhost:5000/api/auth/oauth/callback',
//         client_id: '67990c955eefdf2ff5493cfd-m9id3rb8',
//         client_secret: '5046f197-49b1-4031-a5ae-88117a035caf'
//       }
//     });

//     const { access_token, refresh_token, location_id } = tokenResponse.data;

//     console.log('✅ Access Token:', access_token);
//     console.log('📍 Location ID:', location_id);
//     res.send('✅ Token received and saved successfully.');
//   } catch (error) {
//     console.error('❌ Token Exchange Error:', error.response?.data || error.message);
//     res.status(500).send('Failed to exchange token.');
//   }
// });


// app.listen(PORT, () => {
//   console.log(`🚀 Server running at http://localhost:${PORT}`);
// });

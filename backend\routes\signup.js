const express = require("express");
const cors = require("cors");
const bodyParser = require("body-parser");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const router = express.Router();

const app = express();
const JWT_SECRET = "your-secret-key"; // In production, use environment variables

// Middleware
app.use(cors());
app.use(bodyParser.json());
const {
  initUserTable,
  findUserByEmail,
  createUser,
  saveTokens,
} = require("../models/UserModel");

const {
  REFRESH_SECRET,
  generateAccessToken,
  generateRefreshToken,
} = require("../utils/token");

router.post("/register", async (req, res) => {
  const { name, email, password } = req.body;

  if (!name || !email || !password) {
    return res.status(400).json({ message: "All fields are required" });
  }

  try {
    await initUserTable();

    const existingUser = await findUserByEmail(email);
    if (existingUser) {
      return res.status(400).json({ message: "User already exists" });
    }

    const hashedPassword = bcrypt.hashSync(password, 10);
    const id = Date.now().toString();

    await createUser(id, name, email, hashedPassword);

    const user = { id, email };
    const accessToken = generateAccessToken(user);
    const refreshToken = generateRefreshToken(user);

    await saveTokens(email, accessToken, refreshToken);

    res.status(201).json({
      message: "User registered successfully",
      accessToken,
      refreshToken,
    });
  } catch (err) {
    console.error("Register error:", err.message);
    res.status(500).json({ message: "Internal server error" });
  }
});

router.post("/refresh-token", async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.status(400).json({ message: "Refresh token required" });
  }

  try {
    const payload = jwt.verify(refreshToken, REFRESH_SECRET);
    const user = await findUserByEmail(payload.email);

    if (!user || user.refreshToken !== refreshToken) {
      return res.status(403).json({ message: "Invalid refresh token" });
    }

    const newAccessToken = generateAccessToken(user);
    const newRefreshToken = generateRefreshToken(user);

    await saveTokens(user.email, newAccessToken, newRefreshToken);

    res.json({
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
    });
  } catch (err) {
    console.error("Refresh error:", err.message);
    return res.status(403).json({ message: "Token expired or invalid" });
  }
});




module.exports = router;
import * as React from 'react';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import HomeRoundedIcon from '@mui/icons-material/HomeRounded';
import AnalyticsRoundedIcon from '@mui/icons-material/AnalyticsRounded';
import PeopleRoundedIcon from '@mui/icons-material/PeopleRounded';
import AssignmentRoundedIcon from '@mui/icons-material/AssignmentRounded';
import SettingsRoundedIcon from '@mui/icons-material/SettingsRounded';
import InfoRoundedIcon from '@mui/icons-material/InfoRounded';
import HelpRoundedIcon from '@mui/icons-material/HelpRounded';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

export default function MenuContent() {
  const navigate = useNavigate();
  const location = useLocation();
  const { locationId } = useParams();

  const mainListItems = [
    { text: 'Home', icon: <HomeRoundedIcon />, path: `/${locationId}` },
    { text: 'Contacts', icon: <AnalyticsRoundedIcon />, path: `/${locationId}/contacts` },
    { text: 'Analytics', icon: <AnalyticsRoundedIcon />, path: `/${locationId}/analytics` },
    { text: 'Clients', icon: <PeopleRoundedIcon />, path: `/${locationId}/clients` },
    { text: 'Tasks', icon: <AssignmentRoundedIcon />, path: `/${locationId}/tasks` },
    { text: 'Settings', icon: <SettingsRoundedIcon />, path: `/${locationId}/settings` },
    { text: 'About', icon: <InfoRoundedIcon />, path: `/${locationId}/about` },
    { text: 'Feedback', icon: <HelpRoundedIcon />, path: `/${locationId}/feedback` },
  ];

  const secondaryListItems = [
    { text: 'Settings', icon: <SettingsRoundedIcon />, path: `/${locationId}/settings` },
    { text: 'About', icon: <InfoRoundedIcon />, path: `/${locationId}/about` },
    { text: 'Feedback', icon: <HelpRoundedIcon />, path: `/${locationId}/feedback` },
  ];

  const handleClick = (path) => {
    navigate(path);
  };

  return (
    <Stack sx={{ flexGrow: 1, p: 1, justifyContent: 'space-between' }}>
      <List dense>
        {mainListItems.map((item, index) => (
          <ListItem key={index} disablePadding sx={{ display: 'block' }}>
            <ListItemButton 
              selected={location.pathname === item.path} 
              onClick={() => handleClick(item.path)}
            >
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
      {/* <List dense>
        {secondaryListItems.map((item, index) => (
          <ListItem key={index} disablePadding sx={{ display: 'block' }}>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => handleClick(item.path)}
            >
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List> */}
    </Stack>
  );
}

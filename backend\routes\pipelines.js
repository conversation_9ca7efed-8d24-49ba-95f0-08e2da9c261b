const express = require("express");
const router = express.Router();
require("dotenv").config();

// Ensure native fetch is available (Node.js v18+)
const fetch = global.fetch;

// CORS Middleware
router.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization"
  );
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  if (req.method === "OPTIONS") {
    return res.sendStatus(200);
  }
  next();
});

// GET all pipelines
// router.get("/pipelines", async (req, res) => {
//   try {
//     const authHeader = req.headers.authorization;
//     if (!authHeader || !authHeader.startsWith("Bearer ")) {
//       return res.status(401).json({ error: "Missing or invalid authorization token" });
//     }

//     const apiKey = authHeader.split(" ")[1];
//     const url = "https://rest.gohighlevel.com/v1/pipelines/";

//     const response = await fetch(url, {
//       headers: {
//         Authorization: `Bearer ${apiKey}`,
//       },
//     });

//     const data = await response.json();

//     if (!response.ok) {
//       return res.status(response.status).json({
//         error: "Failed to fetch pipelines",
//         details: data,
//       });
//     }

//     console.log(`Fetched ${data.pipelines?.length || 0} pipelines`);
//     res.json(data);
//   } catch (error) {
//     console.error("Error fetching pipelines:", error);
//     res.status(500).json({ error: "Failed to fetch pipeline data" });
//   }
// });

// router.post("/get-location-access-token/", async (req, res) => {
//   const { locationId } = req.params;
//   const authHeader = req.headers.authorization;
//   const { companyId } = req.body; // Company ID should be provided here

//   console.log(authHeader,'authHeader is ');

//   try {
//     const response = await fetch(
//       "https://services.leadconnectorhq.com/oauth/locationToken",
//       {
//         method: "POST",
//         headers: {
//           Authorization: `Bearer ${authHeader}`,
//           "Content-Type": "application/x-www-form-urlencoded",
//           Version: "2021-07-28",
//           Accept: "application/json",
//         },
//         body: new URLSearchParams({
//           companyId: companyId,
//           locationId: locationId,
//         }).toString(), // Encoding data as URLSearchParams
//       }
//     );

//     const data = await response.json();

//     console.log(
//       response,
//       "<--------- This is a response of get-location-access-token"
//     );

//     if (data.access_token) {
//       res.json({ access_token: data.access_token });
//     } else {
//       res.status(400).json({ error: "Failed to fetch location access token" });
//     }
//   } catch (error) {
//     console.error("Error generating location access token:", error);
//     res.status(500).json({ error: "Server error" });
//   }
// });

router.post("/get-location-access-token", async (req, res) => {
  const { locationId, companyId } = req.body;

  if (!locationId || !companyId) {
    return res
      .status(400)
      .json({ error: "locationId and companyId are required" });
  }

  const agencyToken = req.headers.authorization?.split(" ")[1]; // Bearer token

  if (!agencyToken) {
    return res.status(401).json({ error: "Agency token missing" });
  }

  try {
    const response = await fetch(
      "https://services.leadconnectorhq.com/oauth/locationToken",
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${agencyToken}`,
          Version: "2021-07-28",
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          locationId,
          companyId,
        })
      }
    );

    const data = await response.json();
    return res.status(200).json(data); // contains access_token
  } catch (error) {
    console.error(
      "Error getting location access token:",
      error.message
    );
    return res.status(500).json({
      error: "Failed to get location access token",
      details: error.message,
    });
  }
});

router.get("/pipelines", async (req, res) => {
  const authHeader = req.headers.authorization;
  const { locationId } = req.query;

  // Validate inputs
  if (!authHeader || !locationId) {
    return res
      .status(400)
      .json({ error: "Missing Authorization header or locationId" });
  }

  try {
    const response = await fetch(
      `https://services.leadconnectorhq.com/opportunities/pipelines?locationId=${locationId}`,
      {
        method: "GET",
        headers: {
          Authorization: authHeader,
          Version: "2021-07-28",
          Accept: "application/json",
        },
      }
    );

    // console.log(response.status);

    // console.log(locationId, "<-------- this is location ");
    // console.log(authHeader, "<-------- this is authorization ");

    if (!response.ok) {
      throw new Error(`GHL API error! Status: ${response.status}`);
    }

    const data = await response.json();
    res.json(data);
    // console.log("pipelines Data", data);
  } catch (error) {
    console.error("Error fetching pipelines from GHL:", error);
    res.status(500).json({ error: "Failed to fetch pipelines" });
  }
});

// GET all opportunities for a pipeline (with pagination)
// router.get("/pipelines/:pipelineId/all-opportunities", async (req, res) => {
//   try {
//     const { pipelineId } = req.params;
//     const authHeader = req.headers.authorization;

//     if (!authHeader || !authHeader.startsWith("Bearer ")) {
//       return res
//         .status(401)
//         .json({ error: "Missing or invalid authorization token" });
//     }

//     const apiKey = authHeader.split(" ")[1];

//     let url = `https://rest.gohighlevel.com/v1/pipelines/${pipelineId}/opportunities?limit=100`;
//     let allOpportunities = [];
//     let totalCount = 0;

//     let startAfterId, startAfter, nextPageUrl;

//     while (url) {
//       console.log(`Fetching opportunities from: ${url}`);

//       const response = await fetch(url, {
//         headers: {
//           Authorization: `Bearer ${apiKey}`,
//         },
//       });

//       const data = await response.json();

//       if (!response.ok) {
//         return res.status(response.status).json({
//           error: "Failed to fetch opportunities",
//           details: data,
//         });
//       }

//       const { opportunities = [], meta = {} } = data;
//       totalCount = meta.total || totalCount;
//       startAfterId = meta.startAfterId;
//       startAfter = meta.startAfter;
//       nextPageUrl = meta.nextPageUrl;

//       allOpportunities = [...allOpportunities, ...opportunities];

//       console.log(
//         `Fetched ${opportunities.length} opportunities, total so far: ${allOpportunities.length}`
//       );

//       // Stop if no next page or we've fetched everything
//       if (!nextPageUrl || allOpportunities.length >= totalCount) break;

//       // Update URL with pagination parameters
//       url = `https://rest.gohighlevel.com/v1/pipelines/${pipelineId}/opportunities?limit=100&startAfterId=${startAfterId}&startAfter=${startAfter}`;
//     }

//     // Append pipelineId for reference
//     const opportunitiesWithId = allOpportunities.map((opp) => ({
//       ...opp,
//       pipelineId,
//     }));

//     console.log(
//       `Successfully fetched all ${opportunitiesWithId.length} opportunities`
//     );

//     res.json({
//       opportunities: opportunitiesWithId,
//       meta: {
//         total: totalCount,
//         fetched: opportunitiesWithId.length,
//       },
//     });
//   } catch (error) {
//     console.error("Error fetching opportunities:", error);
//     res
//       .status(500)
//       .json({ error: "Failed to fetch all pipeline opportunities" });
//   }
// });

// router.get("/opportunities/search", async (req, res) => {
//   const { locationId, pipelineIds } = req.body;
//   const authHeader = req.headers.authorization;

//   // if (!authHeader || !locationId || !Array.isArray(pipelineIds)) {
//   if (!authHeader || !locationId ) {
//     return res.status(400).json({ error: "Missing required fields" });
//   }

//   try {
//     const results = [];

//     for (const pipelineId of pipelineIds) {
//       // const queryString = `locationId=${locationId}&pipelineId=${pipelineId}&limit=100`;
//       const queryString = `locationId=${locationId}&limit=100`;

//       const response = await fetch(
//         `https://services.leadconnectorhq.com/opportunities/search?${queryString}`,
//         {
//           method: "POST",
//           headers: {
//             Authorization: authHeader,
//             Version: "2021-07-28",
//             Accept: "application/json",
//             "Content-Type": "application/json",
//           },
//         }
//       );

//       if (!response.ok) {
//         const errorBody = await response.text();
//         console.error(
//           `Error for pipeline ${pipelineId}: ${response.status} - ${errorBody}`
//         );
//         return res.status(response.status).json({
//           error: `Failed to fetch for pipeline ${pipelineId}`,
//           statusCode: response.status,
//           details: errorBody,
//         });
//       }

//       const data = await response.json();

//       results.push({
//         pipelineId,
//         opportunities: data.opportunities || [],
//       });
//     }

//     return res.status(200).json({ opportunities: results });
//   } catch (error) {
//     console.error("Error fetching opportunities:", error.message);
//     return res.status(500).json({
//       error: "Failed to fetch opportunities",
//       details: error.message,
//     });
//   }
// });

router.get("/opportunities/search", async (req, res) => {
  const locationId = req.query.location_id;
  const pipelineIds = req.query.pipelineIds?.split(",") || [];
  const authHeader = req.headers.authorization;

  console.log("Request received with:", {
    locationId,
    pipelineIds,
    hasAuthHeader: !!authHeader
  });

  if (!authHeader || !locationId) {
    console.log("Missing required fields:", { authHeader: !!authHeader, locationId });
    return res.status(400).json({ error: "Missing required fields" });
  }

  try {
    const results = [];

    for (const pipelineId of pipelineIds) {
      console.log(`Fetching opportunities for pipeline: ${pipelineId}`);
      
      let allOpportunities = [];
      let hasMoreData = true;
      let page = 1;
      
      while (hasMoreData) {
        const url = `https://services.leadconnectorhq.com/opportunities/search?location_id=${locationId}&limit=100&page=${page}`;
        
        console.log(`Making request to page ${page}:`, url);

        const response = await fetch(url, {
          method: "GET",
          headers: {
            Authorization: `${authHeader}`,
            Version: "2021-07-28",
            Accept: "application/json",
          },
        });

        console.log("Response status:", response.status);

        if (!response.ok) {
          const errorBody = await response.text();
          console.error(`Error for pipeline ${pipelineId}:`, {
            status: response.status,
            error: errorBody
          });
          return res.status(response.status).json({
            error: `Failed to fetch for pipeline ${pipelineId}`,
            statusCode: response.status,
            details: errorBody,
          });
        }

        const data = await response.json();
        
        // Check if we have opportunities in the response
        if (data.opportunities && data.opportunities.length > 0) {
          allOpportunities = [...allOpportunities, ...data.opportunities];
          
          // Log progress
          console.log(`Fetched ${data.opportunities.length} opportunities for page ${page}. Total so far: ${allOpportunities.length}`);
          
          // Check if we've reached the total count
          if (data.meta && allOpportunities.length >= data.meta.total) {
            hasMoreData = false;
            console.log(`Reached total count of ${data.meta.total} opportunities`);
          } else {
            page++;
          }
        } else {
          hasMoreData = false;
          console.log('No more opportunities found');
        }
      }
      
      results.push({
        pipelineId,
        opportunities: allOpportunities,
        total: allOpportunities.length
      });
    }

    // Log final results summary
    console.log("Final results summary:", {
      totalPipelines: results.length,
      totalOpportunities: results.reduce((sum, result) => sum + result.total, 0)
    });

    return res.status(200).json({ data: results[0] });
  } catch (error) {
    console.error("Error in opportunities search:", {
      message: error.message,
      stack: error.stack
    });
    return res.status(500).json({
      error: "Failed to fetch opportunities",
      details: error.message,
    });
  }
});

module.exports = router;

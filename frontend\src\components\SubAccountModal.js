import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>ield,
  Popper,
  Paper,
  ClickAwayListener,
  CircularProgress,
  Typography,
  Button,
  Box,
  CssBaseline,
} from "@mui/material";
import InfiniteScroll from "react-infinite-scroll-component";
import { useDispatch, useSelector } from "react-redux";
import { fetchLocations } from "../redux/actions/location";
import AppTheme from "../shared-theme/AppTheme";
import { useNavigate } from "react-router-dom";

const ITEM_LIMIT = 10;

export default function SubAccountModal(props) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const locationData = useSelector((state) => state.locations?.locations || []);

  const [selectedAccount, setSelectedAccount] = useState(null);
  const [subAccountModalOpen, setSubAccountModalOpen] = useState(true);

  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState([]);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [searchText, setSearchText] = useState("");

  const NEW_API_URL = process.env.REACT_APP_API_BASE_URL;

  useEffect(() => {
    dispatch(fetchLocations());
  }, [dispatch]);

  useEffect(() => {
    if (Array.isArray(locationData) && locationData.length) {
      setItems([]);
      setPage(0);
      setHasMore(true);
      fetchMoreData(); // Initial load
    }
  }, [locationData]);

  const fetchMoreData = () => {
    if (!Array.isArray(locationData)) return;
    const start = page * ITEM_LIMIT;
    const end = start + ITEM_LIMIT;
    const nextItems = locationData.slice(start, end);
    setItems((prev) => [...prev, ...nextItems]);
    setPage((prev) => prev + 1);
    if (end >= locationData.length) setHasMore(false);
  };

  const handleDropdownClick = (event) => {
    setAnchorEl(event.currentTarget);
    setDropdownOpen(true);
  };

  const handleDropdownClose = () => {
    setDropdownOpen(false);
  };

  const handleSelect = (item) => {
    setSelectedAccount(item);
    setSearchText(item.business?.name || "");
    setDropdownOpen(false);
  };

  const handleNext = async () => {
    if (!selectedAccount) return;

    setLoading(true);

    try {
      const id = selectedAccount.id;
      localStorage.setItem("selectedLocationId", id);

      const rawCompanyId = localStorage.getItem("companyId");
      const ghlAccessToken = localStorage.getItem("ghlAccessToken");
      const ghlaccessToken = localStorage.getItem("ghlaccessToken");

      let agencyToken = ghlAccessToken;
      if (
        !agencyToken ||
        agencyToken === "undefined" ||
        agencyToken === "null" ||
        agencyToken === ""
      ) {
        agencyToken = ghlaccessToken;
      }

      if (!rawCompanyId || !agencyToken) {
        console.error("Missing companyId or ghlAccessToken");
        return;
      }

      const companyId = JSON.parse(rawCompanyId);

      const res = await fetch(`${NEW_API_URL}/api/get-location-access-token/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${agencyToken}`,
        },
        body: JSON.stringify({
          locationId: id,
          companyId: companyId,
        }),
      });

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.message || "API Error");
      }

      localStorage.setItem("locationAccessTokenContact", data.access_token);
      console.log("✅ locationAccessTokenContact:", data.access_token);
      navigate(`/${selectedAccount.id}`);
    } catch (err) {
      console.error("❌ Error:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AppTheme {...props}>
      <CssBaseline enableColorScheme />
      <Box
        sx={{
          borderRadius: 3,
          p: 0,
          m: "0 auto",
          backgroundColor: (theme) =>
            theme.palette.mode === "dark"
              ? theme.palette.background.default
              : "#f2f7fa",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "100vh",
          width: "100%",
        }}
      >
        <Box
          maxWidth="sm"
          sx={{
            p: 0,
            maxWidth: "500px",
            borderRadius: "12px",
            backgroundColor: (theme) =>
              theme.palette.mode === "dark"
                ? theme.palette.background.paper
                : "#fff",
            m: "0 20px",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              p: 3,
              pb: 2,
            }}
          />

          <Box sx={{ px: 3, pb: 3 }}>
            <Typography
              variant="h5"
              component="h2"
              sx={{
                fontWeight: 600,
                mb: 2,
                color: (theme) => theme.palette.text.primary,
                lineHeight: 1.3,
              }}
            >
              Choose the sub-account you want to access.
            </Typography>

            <Typography
              variant="body1"
              sx={{
                color: (theme) => theme.palette.text.primary,
                mb: 3,
                lineHeight: 1.5,
              }}
            >
              This sub-account will be used for managing your leads, contacts,
              and campaigns. You can switch or update this later from your
              dashboard.
            </Typography>

            <ClickAwayListener onClickAway={handleDropdownClose}>
              <div>
                <TextField
                  fullWidth
                  size="medium"
                  variant="outlined"
                  label="Select Location"
                  onClick={handleDropdownClick}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  sx={{
                    "&.MuiFormControl-root .MuiInputLabel-root": {
                      margin: "0",
                      backgroundColor: (theme) =>
                        theme.palette.background.paper,
                      px: 0.5,
                    },
                    "&.MuiFormControl-root .MuiInputBase-root": {
                      height: "50px",
                    },
                  }}
                />

                <Popper
                  open={dropdownOpen}
                  anchorEl={anchorEl}
                  placement="bottom-start"
                >
                  <Paper
                    id="scrollableDiv"
                    sx={{
                      width: "100%",
                      maxHeight: 200,
                      overflow: "auto",
                      mt: 1,
                      boxShadow: (theme) =>
                        theme.palette.mode === "dark"
                          ? "0px 3px 9px 1px #262424"
                          : "0px 3px 9px 1px #cce0f4",
                      border: "1px solid var(--template-palette-divider)",
                      maxWidth: 320,
                      minWidth: 320,
                    }}
                  >
                    {/* <InfiniteScroll
                      dataLength={items.length}
                      next={fetchMoreData}
                      hasMore={hasMore}
                      loader={
                        <Box p={2} textAlign="center">
                          <CircularProgress size={20} />
                        </Box>
                      }
                      scrollableTarget="scrollableDiv"
                    >
                      {items
                        .filter((item) =>
                          item.business?.name
                            ?.toLowerCase()
                            .includes(searchText.toLowerCase())
                        )
                        .map((item) => (
                          <Box
                            key={item.id}
                            onClick={() => handleSelect(item)}
                            sx={{
                              px: 2,
                              py: 1,
                             cursor: "pointer",
                              "&:hover": {
                                backgroundColor: (theme) =>
                                  theme.palette.mode === "dark"
                                    ? "#47536b4d"
                                    : "#fff",
                              },
                              backgroundColor:
                                selectedAccount?.id === item.id
                                  ? (theme) =>
                                      theme.palette.mode === "dark"
                                        ? "#47536b4d"
                                        : "#e3f2fd"
                                  : (theme) => theme.palette.background.paper,
                              backgroundColor: (theme) =>
                                theme.palette.mode === "dark"
                                  ? theme.palette.background.paper
                                  : "#fff",
                            }}
                          >
                            {item.business?.name || "Unnamed"}
                          </Box>
                        ))}
                    </InfiniteScroll> */}
                    {items.filter((item) =>
                      item.business?.name
                        ?.toLowerCase()
                        .includes(searchText.toLowerCase())
                    ).length > 0 ? (
                      <InfiniteScroll
                        dataLength={items.length}
                        next={fetchMoreData}
                        hasMore={hasMore}
                        loader={
                          <Box p={2} textAlign="center">
                            <CircularProgress size={20} />
                          </Box>
                        }
                        scrollableTarget="scrollableDiv"
                      >
                        {items
                          .filter((item) =>
                            item.business?.name
                              ?.toLowerCase()
                              .includes(searchText.toLowerCase())
                          )
                          .map((item) => (
                            <Box
                              key={item.id}
                              onClick={() => handleSelect(item)}
                              sx={{
                                px: 2,
                                py: 1,
                                cursor: "pointer",
                                "&:hover": {
                                  backgroundColor: (theme) =>
                                    theme.palette.mode === "dark"
                                      ? "#47536b4d"
                                      : "#fff",
                                },
                                backgroundColor:
                                  selectedAccount?.id === item.id
                                    ? (theme) =>
                                        theme.palette.mode === "dark"
                                          ? "#47536b4d"
                                          : "#e3f2fd"
                                    : (theme) => theme.palette.background.paper,
                                backgroundColor: (theme) =>
                                  theme.palette.mode === "dark"
                                    ? theme.palette.background.paper
                                    : "#fff",
                              }}
                            >
                              {item.business?.name || "Unnamed"}
                            </Box>
                          ))}
                      </InfiniteScroll>
                    ) : (
                      <Box p={2} textAlign="center">
                        <Typography variant="body2" color="text.secondary">
                          No results found
                          <Box component="span" fontWeight="bold">
                            "{searchText}"
                          </Box>
                        </Typography>
                      </Box>
                    )}
                  </Paper>
                </Popper>
              </div>
            </ClickAwayListener>

            <Button
              fullWidth
              variant={!selectedAccount || loading ? "outlined" : "contained"}
              onClick={handleNext}
              disabled={!selectedAccount || loading}
              sx={{ mt: 1 }}
            >
              {loading ? "Loading..." : "Next"}
            </Button>
          </Box>
        </Box>
      </Box>
    </AppTheme>
  );
}

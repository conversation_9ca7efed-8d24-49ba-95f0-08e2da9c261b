import React, { useEffect, useState } from "react";
import MuiAvatar from "@mui/material/Avatar";
import MuiListItemAvatar from "@mui/material/ListItemAvatar";
import ListItemText from "@mui/material/ListItemText";
import ListItemIcon from "@mui/material/ListItemIcon";
import Select, { selectClasses } from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import Divider from "@mui/material/Divider";
import { styled } from "@mui/material/styles";
import { fetchLocations } from "../../redux/actions/location";
import { useDispatch, useSelector } from "react-redux";
import {
  ListItem,
  ListItemButton,
  Typography,
  CircularProgress,
  Backdrop,
  Stack,
} from "@mui/material";
import { useNavigate, useParams, useLocation } from "react-router-dom";

const Avatar = styled(MuiAvatar)(({ theme }) => ({
  width: 28,
  height: 28,
  backgroundColor: (theme.vars || theme).palette.background.paper,
  color: (theme.vars || theme).palette.text.secondary,
  border: `1px solid ${(theme.vars || theme).palette.divider}`,
}));

const ListItemAvatar = styled(MuiListItemAvatar)({
  minWidth: 0,
  marginRight: 12,
});

export default function SelectContent() {
  const [selectedLocation, setSelectedLocation] = useState("");
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { locationId: currentLocationId } = useParams();
  const location = useLocation();
  const locationData = useSelector((state) => state.locations?.locations);
  const NEW_API_URL = process.env.REACT_APP_API_BASE_URL;

  useEffect(() => {
    dispatch(fetchLocations());
  }, [dispatch]);

  const [selectedId, setSelectedId] = useState(
    () =>
      localStorage.getItem("selectedLocationId") || currentLocationId || null
  );

  // Set initial selected location when locationData is loaded
  useEffect(() => {
    if (locationData && locationData.length > 0 && selectedId) {
      const selected = locationData.find((item) => item.id === selectedId);
      if (selected) {
        setSelectedLocation(selected.business.name);
      }
    }
  }, [locationData, selectedId]);

  const handleChange = (event) => {
    const selectedId = event.target.value;
    setSelectedLocation(
      locationData.find((item) => item.id === selectedId)?.business.name || ""
    );
    handleClick(selectedId);
    setOpen(false); // Close the menu after selection
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClick = async (id) => {
    setLoading(true);
    // 1. Store in localStorage and State
    localStorage.setItem("selectedLocationId", id);
    setSelectedId(id);

    // 2. Prepare other required data
    const rawCompanyId = localStorage.getItem("companyId");
    const ghlAccessToken = localStorage.getItem("ghlAccessToken");
    const ghlaccessToken = localStorage.getItem("ghlaccessToken");

    let agencyToken = ghlAccessToken;
    if (
      !agencyToken ||
      agencyToken === "undefined" ||
      agencyToken === "null" ||
      agencyToken === ""
    ) {
      agencyToken = ghlaccessToken;
    }

    if (!rawCompanyId || !agencyToken) {
      console.error("Missing companyId or ghlAccessToken");
      setLoading(false);
      return;
    }

    const companyId = JSON.parse(rawCompanyId);

    try {
      const res = await fetch(`${NEW_API_URL}/api/get-location-access-token/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${agencyToken}`,
        },
        body: JSON.stringify({
          locationId: id,
          companyId: companyId,
        }),
      });

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.message || "API Error");
      }

      localStorage.setItem("locationAccessTokenContact", data.access_token);
      console.log("✅ locationAccessTokenContact:", data.access_token);

      // Construct the new path, preserving the rest of the URL
      const currentPathSegments = location.pathname
        .split("/")
        .filter((segment) => segment);
      const newPath = `/${id}${
        currentPathSegments.length > 1
          ? "/" + currentPathSegments.slice(1).join("/")
          : ""
      }`;

      navigate(newPath + location.search); // Preserve query parameters
      window.location.reload(); // Refresh the page
    } catch (error) {
      console.error("❌ Error in getLocationAccessToken:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Select
        labelId="company-select"
        id="company-simple-select"
        value={selectedId || ""}
        onChange={handleChange}
        onClose={handleClose}
        onOpen={handleOpen}
        open={open}
        displayEmpty
        inputProps={{ "aria-label": "Select company" }}
        fullWidth
        sx={{
          maxHeight: 56,
          width: "100%",
          "&.MuiList-root": {
            p: "8px",
          },
          [`& .${selectClasses.select}`]: {
            display: "flex",
            alignItems: "center",
            gap: "2px",
            pl: 1,
          },
          // overflow: "hidden",
        }}
      >
        {locationData
          ?.slice()
          .sort((a, b) => a.business.name.localeCompare(b.business.name))
          .map((item) => (
            <MenuItem key={item.id} value={item.id}>
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText
                primary={item.business.name}
                secondary={`${item.business.address}, ${item.business.city}, ${item.business.state}`}
                sx={{
                  overflow: "hidden",
                }}
              />
            </MenuItem>
          ))}
      </Select>
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={loading}
      >
        <Stack direction="column" alignItems="center" spacing={2}>
          <CircularProgress color="inherit" />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading new sub account data...
          </Typography>
        </Stack>
      </Backdrop>
    </>
  );
}

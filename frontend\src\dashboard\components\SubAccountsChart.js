import * as React from 'react';
import PropTypes from 'prop-types';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import Chip from '@mui/material/Chip';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import { BarChart } from '@mui/x-charts/BarChart';
import { useTheme } from '@mui/material/styles';

export default function SubAccountsChart({ locationData = [] }) {
  const theme = useTheme();
  const [timeFilter, setTimeFilter] = React.useState('7d');

  // Function to filter data based on time period
  const getFilteredData = (data, filter) => {
    if (!data || data.length === 0) return [];
    
    const now = new Date();
    let startDate;

    switch (filter) {
      case '1m':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '3m':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '6m':
        startDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
        break;
      default: // 7d
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    return data.filter(location => {
      if (!location.dateAdded) return false;
      const dateAdded = new Date(location.dateAdded);
      return !isNaN(dateAdded.getTime()) && dateAdded >= startDate && dateAdded <= now;
    });
  };

  // Function to group data by date
  const groupDataByDate = (data) => {
    const grouped = {};
    
    data.forEach(location => {
      if (!location.dateAdded) return;
      const date = new Date(location.dateAdded).toISOString().split('T')[0];
      grouped[date] = (grouped[date] || 0) + 1;
    });

    return grouped;
  };

  // Function to create chart data for bar chart (daily counts, not cumulative)
  const createChartData = (filteredData, filter) => {
    if (filteredData.length === 0) return [];

    const grouped = groupDataByDate(filteredData);
    
    // Get the start date for the filter period
    const now = new Date();
    let startDate;
    switch (filter) {
      case '1m':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '3m':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '6m':
        startDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
        break;
      default: // 7d
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Create data points for each day in the range
    const chartData = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= now) {
      const dateStr = currentDate.toISOString().split('T')[0];
      const count = grouped[dateStr] || 0;
      
      chartData.push({
        date: currentDate.toLocaleDateString(),
        count: count
      });
      
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return chartData;
  };

  const filteredData = getFilteredData(locationData, timeFilter);
  const chartData = createChartData(filteredData, timeFilter);
  
  const totalCount = filteredData.length;
  
  // Calculate percentage change (simplified - comparing with previous period)
  const getPreviousPeriodData = (filter) => {
    const now = new Date();
    let startDate, endDate;
    
    switch (filter) {
      case '1m':
        startDate = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);
        endDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '3m':
        startDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
        endDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '6m':
        startDate = new Date(now.getTime() - 360 * 24 * 60 * 60 * 1000);
        endDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
        break;
      default: // 7d
        startDate = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
        endDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    return locationData.filter(location => {
      if (!location.dateAdded) return false;
      const dateAdded = new Date(location.dateAdded);
      return !isNaN(dateAdded.getTime()) && dateAdded >= startDate && dateAdded <= endDate;
    });
  };

  const previousPeriodData = getPreviousPeriodData(timeFilter);
  const previousCount = previousPeriodData.length;
  const percentageChange = previousCount > 0 ? ((totalCount - previousCount) / previousCount) * 100 : 0;

  const handleTimeFilterChange = (event, newFilter) => {
    if (newFilter !== null) {
      setTimeFilter(newFilter);
    }
  };

  const getTimeLabel = (filter) => {
    switch (filter) {
      case '7d': return '7 days';
      case '1m': return '1 month';
      case '3m': return '3 months';
      case '6m': return '6 months';
      default: return '7 days';
    }
  };

  return (
    <Card variant="outlined" sx={{ width: '100%' }}>
      <CardContent>
        <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
          <Typography component="h2" variant="subtitle2">
            Sub-accounts Growth
          </Typography>
          <ToggleButtonGroup
            value={timeFilter}
            exclusive
            onChange={handleTimeFilterChange}
            size="small"
            sx={{
              boxShadow:'none'
            }}
          >
            <ToggleButton value="7d">7D</ToggleButton>
            <ToggleButton value="1m">1M</ToggleButton>
            <ToggleButton value="3m">3M</ToggleButton>
            <ToggleButton value="6m">6M</ToggleButton>
          </ToggleButtonGroup>
        </Stack>
        
        <Stack sx={{ justifyContent: 'space-between', mb: 2 }}>
          <Stack
            direction="row"
            sx={{
              alignContent: { xs: 'center', sm: 'flex-start' },
              alignItems: 'center',
              gap: 1,
            }}
          >
            <Typography variant="h4" component="p">
              {totalCount}
            </Typography>
            <Chip 
              size="small" 
              color={percentageChange >= 0 ? 'success' : 'error'} 
              label={`${percentageChange >= 0 ? '+' : ''}${percentageChange.toFixed(1)}%`} 
            />
          </Stack>
          <Typography variant="caption" sx={{ color: 'text.secondary' }}>
            Sub-accounts added in the last {getTimeLabel(timeFilter)}
          </Typography>
        </Stack>

        {chartData.length > 0 ? (
          <BarChart
            xAxis={[
              {
                dataKey: 'date',
                scaleType: 'band',
                tickLabelStyle: {
                  angle: -45,
                  textAnchor: 'end',
                },
              },
            ]}
            yAxis={[
              {
                label: 'Sub-accounts Added',
              },
            ]}
            series={[
              {
                dataKey: 'count',
                label: 'Sub-accounts',
                color: theme.palette.primary.main,
              },
            ]}
            dataset={chartData}
            height={250}
            margin={{ left: 50, right: 20, top: 20, bottom: 60 }}
            slotProps={{
              legend: {
                hidden: true,
              },
            }}
          />
        ) : (
          <Stack 
            justifyContent="center" 
            alignItems="center" 
            sx={{ height: 250, color: 'text.secondary' }}
          >
            <Typography variant="body2">No data available for selected period</Typography>
          </Stack>
        )}
      </CardContent>
    </Card>
  );
}

SubAccountsChart.propTypes = {
  locationData: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      dateAdded: PropTypes.string,
      name: PropTypes.string,
      country: PropTypes.string,
    })
  ),
};
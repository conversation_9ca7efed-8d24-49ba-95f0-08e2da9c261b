// import * as React from "react";
// import { useParams } from "react-router-dom";
// import { format } from "date-fns";
// import { alpha } from "@mui/material/styles";
// import CssBaseline from "@mui/material/CssBaseline";
// import Box from "@mui/material/Box";
// import Stack from "@mui/material/Stack";
// import AppNavbar from "../dashboard/components/AppNavbar";
// import Header from "../dashboard/components/Header";
// import SideMenu from "../dashboard/components/SideMenu";
// import { fetchOpportunities } from '../services/opportunities';
// import { processOpportunitiesData } from '../services/opportunities';
// import AppTheme from "../shared-theme/AppTheme";
// import {
//   // chartsCustomizations,
//   dataGridCustomizations,
//   datePickersCustomizations,
//   treeViewCustomizations,
// } from "../dashboard/theme/customizations/index";
// import chartsCustomizations from '../dashboard/theme/customizations/charts'
// import { useDispatch, useSelector } from "react-redux";
// import { fetchLocations } from "../redux/actions/location";
// import {
//   Button,
//   Typography,
//   CircularProgress,
//   Card,
//   CardContent,
//   Chip,
//   Alert,
//   FormControl,
//   InputLabel,
//   Select,
//   MenuItem,
//   Drawer,
//   IconButton,
//   Divider,
//   List,
//   ListItemText,
//   ListItemButton,
//   ListItemIcon,
//   useMediaQuery,
//   Checkbox,
//   Popover,
//   Tooltip,
//   TextField,
//   InputAdornment,
//   Avatar,
//   Badge,
//   RadioGroup,
//   FormControlLabel,
//   Radio,
//   Accordion,
//   AccordionSummary,
//   AccordionDetails,
//   Tabs,
//   Tab,
//   tooltipClasses,
// } from "@mui/material";
// import { locationservice } from "../services/locationservice";
// import CloseIcon from "@mui/icons-material/Close";
// import ChevronRightIcon from "@mui/icons-material/ChevronRight";
// import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
// import FilterAltOutlinedIcon from "@mui/icons-material/FilterAltOutlined";
// import {
//   mockFields,
//   menuOptions,
//   sortOptions,
//   allFieldValues,
// } from "../contant/mockData";
// import SwapVertOutlinedIcon from "@mui/icons-material/SwapVertOutlined";
// import BackspaceOutlinedIcon from "@mui/icons-material/BackspaceOutlined";
// import SearchOutlinedIcon from "@mui/icons-material/SearchOutlined";
// import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
// import CallOutlinedIcon from "@mui/icons-material/CallOutlined";
// import SmsOutlinedIcon from "@mui/icons-material/SmsOutlined";
// import LocalOfferOutlinedIcon from "@mui/icons-material/LocalOfferOutlined";
// import DescriptionOutlinedIcon from "@mui/icons-material/DescriptionOutlined";
// import CheckBoxOutlinedIcon from "@mui/icons-material/CheckBoxOutlined";
// import CalendarMonthOutlinedIcon from "@mui/icons-material/CalendarMonthOutlined";
// import ExpandMoreOutlinedIcon from "@mui/icons-material/ExpandMoreOutlined";
// import FilterListIcon from "@mui/icons-material/FilterList";
// import ManageFieldsDrawer from "./ManageFieldsDrawer";
// import Search from "../dashboard/components/Search";
// import { useEffect } from "react";

// const xThemeComponents = {
//   ...chartsCustomizations,
//   ...dataGridCustomizations,
//   ...datePickersCustomizations,
//   ...treeViewCustomizations,
// };

// export default function SubAccountDetail(props) {
//   const { id } = useParams();
//   const dispatch = useDispatch();
//   const [pipelineStages, setPipelineStages] = React.useState([]);
//   const [stageOpportunities, setStageOpportunities] = React.useState({});
//   const [allOpportunities, setAllOpportunities] = React.useState([]);
//   const [loading, setLoading] = React.useState(true);
//   const [error, setError] = React.useState(null);
//   const [pipelineId, setPipelineId] = React.useState(null);
//   const [pipelineName, setPipelineName] = React.useState("");
//   const [totalOpportunities, setTotalOpportunities] = React.useState(0);
//   const [viewMode, setViewMode] = React.useState("kanban"); // kanban or list
//   const [activeStageId, setActiveStageId] = React.useState("");
//   const [activeStage, setActiveStage] = React.useState(null);
//   const [debugInfo, setDebugInfo] = React.useState(null); // For debugging

//   const locationData = useSelector((state) => state.locations?.locations);

//   React.useEffect(() => {
//     dispatch(fetchLocations());
//   }, [dispatch]);

//   // API key for all API calls
//   const ghlApiKey =
//     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6Ikx3V3hmcWY4N2NYN1hQMldpcW1jIiwiY29tcGFueV9pZCI6IjA5bnRJc1JieDRnQXhjaGJIcWU3IiwidmVyc2lvbiI6MSwiaWF0IjoxNjU4ODMzNTgwODI1LCJzdWIiOiJ1c2VyX2lkIn0.xDo-pGLuK4QPjLDcy6sbXjKRjx3V3A-qO_X9TjUS_-Y";

//   const fetchPipelineData = async () => {
//     try {
//       // console.log("Starting fetchPipelineData function");
//       setLoading(true);
//       setError(null);
//       setAllOpportunities([]);
//       setStageOpportunities({});
//       setTotalOpportunities(0);
//       setDebugInfo("Starting pipeline data fetch");

//       // console.log("Fetching pipeline data with API key");
//       const response = await locationservice.getPipeline();
//       // console.log("Pipeline response:", response);
//       setDebugInfo(`Pipeline response received: ${JSON.stringify(response)}`);

//       if (response?.pipelines && response.pipelines.length > 0) {
//         const pipeline = response.pipelines[0];
//         setPipelineId(pipeline.id);
//         setPipelineName(pipeline.name || "Pipeline");
//         setDebugInfo(`Pipeline found: ${pipeline.name} (${pipeline.id})`);

//         if (pipeline.stages && pipeline.stages.length > 0) {
//           const sortedStages = [...pipeline.stages].sort(
//             (a, b) => (a.position || 0) - (b.position || 0)
//           );
//           setPipelineStages(sortedStages);
//           // console.log("Sorted stages:", sortedStages);
//           setDebugInfo(`Found ${sortedStages.length} stages, fetching opportunities...`);

//           // Use a small delay to ensure state updates before fetching opportunities
//           setTimeout(() => {
//             fetchAllOpportunities(pipeline.id, sortedStages);
//           }, 100);
//         } else {
//           setError("No stages found in the pipeline");
//           setDebugInfo("Error: No stages found in pipeline");
//           setLoading(false);
//         }
//       } else {
//         setError("No pipelines found");
//         setDebugInfo("Error: No pipelines found in response");
//         setLoading(false);
//       }
//     } catch (err) {
//       console.error("Error fetching pipeline data:", err);
//       setError(`Failed to fetch pipeline data: ${err.message}`);
//       setDebugInfo(`Error in fetchPipelineData: ${err.message}`);
//       setLoading(false);
//     }
//   };

//   const fetchAllOpportunities = async (pipelineId, stages) => {
//     try {
//       setDebugInfo(`Fetching opportunities for pipeline: ${pipelineId}`);
//       setLoading(true);

//       const response = await locationservice.getAllPipelineOpportunities();

//       if (!response || !response.success) {
//         throw new Error('Failed to fetch opportunities');
//       }

//       // Process opportunities by stage
//       const opportunitiesByStage = {};
//       stages.forEach(stage => {
//         opportunitiesByStage[stage.id] = [];
//       });

//       // Sort opportunities into their respective stages
//       if (response.data && response.data.opportunities) {
//         response.data.opportunities.forEach(opportunity => {
//           const stageId = opportunity.stageId || opportunity.pipelineStageId;
//           if (opportunitiesByStage[stageId]) {
//             opportunitiesByStage[stageId].push(opportunity);
//           }
//         });
//       }

//       // Update state with all opportunities
//       setStageOpportunities(opportunitiesByStage);
//       setAllOpportunities(response.data?.opportunities || []);
//       setTotalOpportunities(response.meta.total);
//       setDebugInfo(`Successfully loaded ${response.meta.total} opportunities`);
//       setLoading(false);

//     } catch (err) {
//       console.error("Error fetching all opportunities:", err);
//       setError(`Failed to fetch opportunities: ${err.message}`);
//       setDebugInfo(`Error in fetchAllOpportunities: ${err.message}`);
//       setLoading(false);

//       // Initialize empty opportunities by stage
//       const emptyOpportunitiesByStage = {};
//       stages.forEach((stage) => {
//         emptyOpportunitiesByStage[stage.id] = [];
//       });
//       setStageOpportunities(emptyOpportunitiesByStage);
//     }
//   };

//   // Toggle view mode between kanban and list
//   const toggleViewMode = () => {
//     setViewMode(viewMode === "kanban" ? "list" : "kanban");
//   };

//   // Handle stage selection change
//   const handleStageChange = (event) => {
//     const stageId = event.target.value;
//     setActiveStageId(stageId);

//     // Find the selected stage object
//     if (stageId) {
//       const stage = pipelineStages.find((s) => s.id === stageId);
//       setActiveStage(stage);
//     } else {
//       setActiveStage(null);
//     }
//   };

//   // Initial data fetch
//   React.useEffect(() => {
//     //    console.log("SubAccountDetail component mounted with ID:", id);
//     setDebugInfo(`Component mounted with ID: ${id}`);
//     fetchPipelineData();
//   }, [id]);

//   // Opportunity card component
//   const OpportunityCard = ({ opportunity }) => {
//     return (
//       <Card sx={{ mb: 2, boxShadow: 1, marginRight: "5px" }}>
//         <CardContent sx={{ px: 2, "&:last-child": { pb: 2 } }}>
//           <Box sx={{ textAlign: "right" }}>
//             {opportunity.status && (
//               <Chip
//                 label={opportunity.status}
//                 size="small"
//                 color={
//                   opportunity.status === "open"
//                     ? "primary"
//                     : opportunity.status === "won"
//                       ? "success"
//                       : "default"
//                 }
//                 variant="outlined"
//                 sx={{ fontSize: "0.7rem", height: "20px" }}
//               />
//             )}
//           </Box>
//           <Typography variant="subtitle1" fontWeight="600" color="#0b0e14">
//             {opportunity.name || "Untitled Opportunity"}
//           </Typography>

//           {(opportunity.monetaryValue > 0 || opportunity.value > 0) && (
//             <>
//               <Box sx={{ display: "flex", gap: "10px", alignItems: "center" }}>
//                 <span
//                   style={{
//                     color: "#607179",
//                     fontWeight: "600",
//                     mt: 1,
//                     fontSize: "0.75rem",
//                   }}
//                 >
//                   Opportunity Value:
//                 </span>
//                 <Typography
//                   variant="body2"
//                   sx={{ color: "success.main", fontWeight: "bold" }}
//                 >
//                   $
//                   {parseFloat(
//                     opportunity.monetaryValue || opportunity.value
//                   ).toLocaleString(undefined, {
//                     minimumFractionDigits: 2,
//                     maximumFractionDigits: 2,
//                   })}
//                 </Typography>
//               </Box>
//             </>
//           )}

//           {opportunity.source && (
//             <Typography
//               variant="body2"
//               sx={{ mt: 1, fontSize: "0.75rem" }}
//             >
//               <span style={{ color: "#607179", fontWeight: "600" }}>
//                 Opportunity Source:
//               </span>{" "}
//               {opportunity.source}
//             </Typography>
//           )}
//           <Box
//             mt={3}
//             display="flex"
//             alignItems="center"
//             gap={1.5}
//             flexWrap="wrap"
//           >
//             <Tooltip title="CALL" arrow>
//               <CallOutlinedIcon sx={{ width: 16, height: 16 }} />
//             </Tooltip>

//             <Tooltip title="View Conversation" arrow>
//               <SmsOutlinedIcon sx={{ width: 16, height: 16 }} />
//             </Tooltip>

//             <Tooltip title="Add Tags" arrow>
//               <LocalOfferOutlinedIcon sx={{ width: 16, height: 16 }} />
//             </Tooltip>

//             <Tooltip title="Add Note" arrow>
//               <DescriptionOutlinedIcon sx={{ width: 16, height: 16 }} />
//             </Tooltip>

//             <Tooltip title="Add Task" arrow>
//               <CheckBoxOutlinedIcon sx={{ width: 16, height: 16 }} />
//             </Tooltip>
//             <Tooltip title="Add Appointment" arrow>
//               <CalendarMonthOutlinedIcon sx={{ width: 16, height: 16 }} />
//             </Tooltip>
//           </Box>
//         </CardContent>
//       </Card>
//     );
//   };

//   // Count opportunities per stage
//   const countByStage = React.useMemo(() => {
//     const counts = {};

//     // Add count for each stage
//     pipelineStages.forEach((stage) => {
//       counts[stage.id] = stageOpportunities[stage.id]?.length || 0;
//     });

//     // Add total count
//     counts.all = allOpportunities.length;

//     return counts;
//   }, [pipelineStages, stageOpportunities, allOpportunities]);

//   // Get current filtered opportunities
//   const currentOpportunities = React.useMemo(() => {
//     if (!activeStageId) {
//       return allOpportunities;
//     }
//     return stageOpportunities[activeStageId] || [];
//   }, [activeStageId, allOpportunities, stageOpportunities]);

//   const [filter, setFilterOpen] = React.useState(false);
//   const [activeFilterMenu, setActiveFilterMenu] = React.useState(null); // null = show fields list
//   const [selectedFilterOptions, setSelectedFilterOptions] = React.useState({});
//   const isSmallScreen = useMediaQuery("(max-width:600px)");

//   const toggleFilterDrawer = (newOpen) => () => setFilterOpen(newOpen);

//   const handleFieldClick = (fieldId) => setActiveFilterMenu(fieldId);

//   const handleBack = () => setActiveFilterMenu(null);

//   const handleFilterCheckboxChange = (fieldId, optionId) => {
//     setSelectedFilterOptions((prev) => {
//       const current = prev[fieldId] || [];
//       const updated = current.includes(optionId)
//         ? current.filter((id) => id !== optionId)
//         : [...current, optionId];
//       return { ...prev, [fieldId]: updated };
//     });
//   };

//   const FilterDrawerList = (
//     <Box
//       role="presentation"
//       display="flex"
//       flexDirection="column"
//       height="100%"
//     >
//       <Box px={3} py={2} flexShrink={0}>
//         <Stack direction="row" justifyContent="space-between">
//           <Box>
//             <Typography variant="h4">Filters</Typography>
//             <Typography variant="body2" color="text.disabled">
//               Apply filters to opportunities
//             </Typography>
//           </Box>
//           <Box>
//             <IconButton onClick={toggleFilterDrawer(false)}>
//               <CloseIcon />
//             </IconButton>
//           </Box>
//         </Stack>
//       </Box>

//       <Divider />
//       <Box flexGrow={1} overflow="auto" sx={{ px: 2, pt: 2 }}>
//         {!activeFilterMenu && (
//           <List>
//             {mockFields.map((field) => (
//               <ListItemButton
//                 key={field.id}
//                 sx={{
//                   mb: "15px",
//                   display: "flex",
//                   justifyContent: "space-between",
//                   borderRadius: "8px",
//                   backgroundColor: "#f2f4f7",
//                 }}
//                 onClick={() => handleFieldClick(field.id)}
//               >
//                 <ListItemText primary={field.label} />
//                 <ListItemIcon>
//                   <ChevronRightIcon />
//                 </ListItemIcon>
//               </ListItemButton>
//             ))}
//           </List>
//         )}

//         {activeFilterMenu && (
//           <Box>
//             <Button
//               startIcon={<ArrowBackIosIcon />}
//               onClick={handleBack}
//               sx={{ mb: 1, textTransform: "none" }}
//             >
//               Back
//             </Button>

//             <Typography variant="h6" mb={2}>
//               {mockFields.find((f) => f.id === activeFilterMenu)?.label}
//             </Typography>

//             <List>
//               {(menuOptions[activeFilterMenu] || []).map((option) => (
//                 <ListItemButton
//                   key={option.id}
//                   onClick={() =>
//                     handleFilterCheckboxChange(activeFilterMenu, option.id)
//                   }
//                 >
//                   <Checkbox
//                     edge="start"
//                     checked={(
//                       selectedFilterOptions[activeFilterMenu] || []
//                     ).includes(option.id)}
//                   />
//                   <ListItemText primary={option.label} />
//                 </ListItemButton>
//               ))}
//             </List>
//           </Box>
//         )}
//       </Box>

//       <Box px={3} py={2} flexShrink={0}>
//         <Stack direction="row" gap={1} justifyContent="end">
//           <Button variant="outlined" onClick={toggleFilterDrawer(false)}>
//             Cancel
//           </Button>
//           <Button variant="contained">Apply</Button>
//         </Stack>
//       </Box>
//     </Box>
//   );

//   const [anchorEl, setAnchorEl] = React.useState(null);
//   const [sortOption, setSortOption] = React.useState("");
//   const openSort = Boolean(anchorEl);
//   const handleClick = (event) => {
//     setAnchorEl(event.currentTarget);
//   };

//   const handleClose = () => {
//     setAnchorEl(null);
//   };

//   const handleClear = () => {
//     setSortOption("");
//   };

//   const handleChange = (event) => {
//     setSortOption(event.target.value);
//   };

//   const open = Boolean(anchorEl);
//   const popId = open ? "sort-popover" : undefined;

//   const sortPopUp = (
//     <Popover
//       id={popId}
//       open={open}
//       anchorEl={anchorEl}
//       onClose={handleClose}
//       anchorOrigin={{
//         vertical: "bottom",
//         horizontal: "left",
//       }}
//       sx={{ mt: 1 }}
//     >
//       {/* Header with Title & Clear Button */}
//       <Box
//         display="flex"
//         justifyContent="space-between"
//         alignItems="center"
//         px={2}
//         py={1.5}
//         minWidth={280}
//       >
//         <Typography variant="subtitle1" fontWeight={600}>
//           Sort By
//         </Typography>
//         <Button color="info" size="small" onClick={handleClear}>
//           Clear
//         </Button>
//       </Box>

//       {/* Select Dropdown with Fixed Height & Tooltip */}
//       <Box px={2} pb={2}>
//         <FormControl fullWidth size="small">
//           <InputLabel>Sort By</InputLabel>
//           <Select
//             value={sortOption}
//             onChange={handleChange}
//             MenuProps={{
//               PaperProps: { sx: { maxHeight: 250 } }, // Fixed height with scroll
//             }}
//             sx={{ width: "100%" }} // Ensure full width
//           >
//             {sortOptions.map((option) => (
//               <MenuItem
//                 key={option.value}
//                 value={option.value}
//                 sx={{
//                   maxWidth: 300,
//                   overflow: "hidden",
//                   textOverflow: "ellipsis",
//                   whiteSpace: "nowrap",
//                 }}
//               >
//                 {option.label}
//               </MenuItem>
//             ))}
//           </Select>
//         </FormControl>
//       </Box>
//     </Popover>
//   );

//   const [manageFieldsOpen, setManageFieldsOpen] = React.useState(false);
//   const toggleManageFieldsDrawer = (newOpen) => () => {
//     // console.log("Drawer toggled:", newOpen ? "Opened" : "Closed");
//     setManageFieldsOpen(newOpen);
//   };

//   const locationId = props.subAccount?.id || props.locationId;
//   const locationAccessToken = props.subAccount?.accessToken || props.locationAccessToken;

//   const [pipelines, setPipelines] = React.useState([]);
//   const [pipelineIds, setPipelineIds] = React.useState([]);
//   const [opportunitiesByPipeline, setOpportunitiesByPipeline] = React.useState([]);
//   const [isLoading, setIsLoading] = React.useState(false);

//   // Fetch pipelines first (if needed)
//   React.useEffect(() => {
//     const fetchPipelines = async () => {
//       try {
//         // Your existing pipeline fetching code
//         // const pipelinesData = await fetchPipelines(locationId, locationAccessToken);
//         // setPipelines(pipelinesData);
//         // Extract just the IDs for the opportunities API call
//         // const ids = pipelinesData.map(pipeline => pipeline.id);
//         // setPipelineIds(ids);

//         // For demonstration purposes:
//         const mockPipelines = [
//           { id: 'pipeline1', name: 'Sales Pipeline' },
//           { id: 'pipeline2', name: 'Marketing Pipeline' }
//         ];
//         setPipelines(mockPipelines);
//         setPipelineIds(mockPipelines.map(p => p.id));
//       } catch (error) {
//         console.error('Error fetching pipelines:', error);
//         setError('Failed to load pipelines');
//       }
//     };

//     if (locationId && locationAccessToken) {
//       fetchPipelines();
//     }
//   }, [locationId, locationAccessToken]);

//   // Load opportunities once we have pipeline IDs
//   const loadOpportunities = async () => {
//     if (!locationId || !locationAccessToken || pipelineIds.length === 0) {
//       console.warn('Missing required data to fetch opportunities');
//       return;
//     }

//     setIsLoading(true);
//     setError(null);

//     try {
//       // 1. Fetch raw data from API
//       const rawData = await fetchOpportunities(locationId, pipelineIds, locationAccessToken);

//       if (!rawData) {
//         setError('Failed to fetch opportunities data');
//         return;
//       }

//       // 2. Process the data with our pipelines information
//       const organizedData = processOpportunitiesData(rawData, pipelines);

//       // 3. Update your component state
//       setOpportunitiesByPipeline(organizedData);

//       // Optional: Log the final structure
//       console.log("Final organized opportunities:", organizedData);
//     } catch (error) {
//       console.error('Error loading opportunities:', error);
//       setError('Failed to load opportunities');
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Load opportunities when pipeline IDs are available
//   useEffect(() => {
//     if (pipelineIds.length > 0) {
//       loadOpportunities();
//     }
//   }, [pipelineIds]); // Or add other dependencies as needed

//   return (
//     <AppTheme {...props} themeComponents={xThemeComponents}>
//       <CssBaseline enableColorScheme />
//       <Box sx={{ display: "flex" }}>
//         <SideMenu />
//         <AppNavbar />
//         <Box
//           component="main"
//           sx={(theme) => ({
//             flexGrow: 1,
//             backgroundColor: theme.vars
//               ? `rgba(${theme.vars.palette.background.defaultChannel} / 1)`
//               : alpha(theme.palette.background.default, 1),
//             overflow: "auto",
//             height: "100vh",
//           })}
//         >
//           <Header title="Sub-Account-Detail" />
//           <Stack
//             spacing={2}
//             sx={{
//               alignItems: "center",
//               padding: "1rem 1rem 0rem 1rem",
//               mt: { xs: 8, md: 0 },
//             }}
//           >
//             <Box
//               sx={{
//                 display: "flex",
//                 justifyContent: "space-between",
//                 alignItems: "center",
//                 width: "100%",
//                 flexWrap: "wrap",
//               }}
//             >
//               <Typography variant="h5" sx={{ fontWeight: "bold" }}>
//                 {pipelineName}
//               </Typography>
//               <Typography variant="body2" color="text.secondary">
//                 Account ID: {id}
//               </Typography>
//             </Box>

//             {error && (
//               <Alert severity="error" sx={{ width: "100%" }}>
//                 {error}
//               </Alert>
//             )}

//             {/* Controls and filters */}
//             <Box
//               sx={{
//                 display: "flex",
//                 justifyContent: "space-between",
//                 alignItems: "center",
//                 margin: "auto",
//                 width: "100%",
//                 flexWrap: "wrap",
//                 gap: 1,
//               }}
//             >
//               <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
//                 <FormControl size="small" sx={{ minWidth: 200 }}>
//                   <InputLabel>Filter by Stage</InputLabel>
//                   <Select
//                     value={activeStageId}
//                     label="Filter by Stage"
//                     onChange={handleStageChange}
//                   >
//                     <MenuItem value="">
//                       All Stages ({countByStage.all || 0})
//                     </MenuItem>
//                     {pipelineStages.map((stage) => (
//                       <MenuItem key={stage.id} value={stage.id}>
//                         {stage.name} ({countByStage[stage.id] || 0})
//                       </MenuItem>
//                     ))}
//                   </Select>
//                 </FormControl>

//                 <Button
//                   size="small"
//                   variant="outlined"
//                   onClick={toggleViewMode}
//                   sx={{ ml: 1 }}
//                 >
//                   {viewMode === "kanban" ? "List View" : "Kanban View"}
//                 </Button>
//                 <Button
//                   variant="outlined"
//                   startIcon={<FilterListIcon />}
//                   onClick={handleClick}
//                 >
//                   Sort
//                 </Button>

//                 <Popover
//                   open={openSort}
//                   anchorEl={anchorEl}
//                   onClose={handleClose}
//                   anchorOrigin={{
//                     vertical: "bottom",
//                     horizontal: "left",
//                   }}
//                   transformOrigin={{
//                     vertical: "top",
//                     horizontal: "left",
//                   }}
//                 >
//                   <Box sx={{ p: 2, minWidth: 250 }}>
//                     <Typography
//                       variant="subtitle1"
//                       sx={{ fontWeight: "bold", mb: 1 }}
//                     >
//                       Sort By
//                     </Typography>
//                     <FormControl fullWidth size="small">
//                       <InputLabel>Sort By</InputLabel>
//                       <Select
//                         value={props.activeSort}
//                         onChange={(e) => {
//                           // handleSortChange(e);
//                           handleClose();
//                         }}
//                       >
//                         {sortOptions.map((option) => (
//                           <MenuItem key={option.value} value={option.value}>
//                             {option.label}
//                           </MenuItem>
//                         ))}
//                       </Select>
//                     </FormControl>
//                   </Box>
//                 </Popover>

//                 <Search width="45ch" />
//               </Box>
//               {/* <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
//                 <FormControl size="small" sx={{ minWidth: 200 }}>
//                   <InputLabel>Filter by Stage</InputLabel>
//                   <Select
//                     value={activeStageId}
//                     label="Filter by Stage"
//                     onChange={handleStageChange}
//                   >
//                     <MenuItem value="">
//                       All Stages ({countByStage.all || 0})
//                     </MenuItem>
//                     {pipelineStages.map((stage) => (
//                       <MenuItem key={stage.id} value={stage.id}>
//                         {stage.name} ({countByStage[stage.id] || 0})
//                       </MenuItem>
//                     ))}
//                   </Select>
//                 </FormControl>

//                 <Button
//                   size="small"
//                   variant="outlined"
//                   onClick={toggleViewMode}
//                   sx={{ ml: 1 }}
//                 >
//                   {viewMode === "kanban" ? "List View" : "Kanban View"}
//                 </Button>
//               </Box> */}

//               <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
//                 {allOpportunities.length > 0 && (
//                   <Typography variant="body2" color="text.secondary">
//                     Loaded {allOpportunities.length} of {totalOpportunities}{" "}
//                     opportunities
//                   </Typography>
//                 )}

//                 <Button
//                   variant="contained"
//                   color="primary"
//                   onClick={fetchPipelineData}
//                   disabled={loading}
//                 >
//                   {loading ? (
//                     <CircularProgress size={24} color="inherit" />
//                   ) : (
//                     "Refresh Data"
//                   )}
//                 </Button>
//               </Box>
//             </Box>
//             <Stack
//               sx={{
//                 display: "flex",
//                 justifyContent: "space-between",
//                 alignItems: "center",
//                 // direction: "",
//                 flexDirection: "row",
//                 width: "100%",
//               }}
//             >
//               <Box
//                 sx={{
//                   display: "flex",
//                   alignItems: "center",
//                   flexWrap: "wrap",
//                   gap: 2,
//                 }}
//               >
//                 <Chip
//                   icon={<FilterAltOutlinedIcon />}
//                   label="Advanced Filters"
//                   size={isSmallScreen ? "small" : "medium"}
//                   variant="outlined"
//                   onClick={toggleFilterDrawer(true)}
//                 />
//                 <Chip
//                   icon={<SwapVertOutlinedIcon />}
//                   label={`Sort ${sortOption ? "(1)" : ""}`}
//                   size="medium"
//                   variant="outlined"
//                   onClick={handleClick}
//                   aria-describedby={popId}
//                 />
//               </Box>

//               <Box display="flex" alignItems="center" gap={2}>
//                 {/* Search Bar */}
//                 <TextField
//                   size="small"
//                   placeholder="Search Opportunities"
//                   variant="outlined"
//                   sx={{
//                     width: 250, // Adjust width as needed
//                     "& .MuiOutlinedInput-root": {
//                       borderRadius: "8px",
//                       backgroundColor: "#f8f9fb", // Light background
//                     },
//                   }}
//                   InputProps={{
//                     startAdornment: (
//                       <InputAdornment position="start">
//                         <SearchOutlinedIcon color="disabled" />
//                       </InputAdornment>
//                     ),
//                   }}
//                 />

//                 {/* Manage Fields Button */}
//                 <Button
//                   variant="outlined"
//                   onClick={toggleManageFieldsDrawer(true, "Button Click")}
//                 >
//                   <SettingsOutlinedIcon fontSize="small" />
//                   <Typography variant="body2" fontWeight={500} sx={{ ml: 0.5 }}>
//                     Manage Fields
//                   </Typography>
//                 </Button>
//               </Box>
//             </Stack>

//             {loading ? (
//               <Box
//                 sx={{
//                   display: "flex",
//                   flexDirection: "column",
//                   alignItems: "center",
//                   justifyContent: "center",
//                   my: 4,
//                 }}
//               >
//                 <CircularProgress />
//                 <Typography variant="body1" sx={{ mt: 2 }}>
//                   Loading all opportunities... This may take a moment.
//                 </Typography>
//               </Box>
//             ) : (
//               <>
//                 {viewMode === "kanban" ? (
//                   <Box
//                     sx={{
//                       display: "flex",
//                       gap: 2,
//                       padding: "16px 0px",
//                       overflowX: "hidden",
//                       width: "100%",
//                       maxWidth: { sm: "auto", md: "1700px" },
//                       pb: 4,
//                       height: "calc(100vh - 220px)",
//                       "&:hover": {
//                         overflowX: "auto",
//                         scrollbarWidth: "thin",
//                       },
//                     }}
//                   >
//                     {pipelineStages.map((stage) => {
//                       const opportunities = stageOpportunities[stage.id] || [];
//                       console.log("Stage Opportunities:", stageOpportunities);
//                       console.log(`Fetching opportunities for stage ${stage.name} (ID: ${stage.id})`);
//                       console.log("Opportunities for this stage:", opportunities);


//                       return (
//                         <Box
//                           key={stage.id}
//                           sx={{
//                             minWidth: "340px",
//                             maxWidth: "400px",
//                             backgroundColor: "#f8f9fa",
//                             borderRadius: 2,
//                             padding: 2,
//                             boxShadow: 2,
//                             height: "fit-content",
//                             maxHeight: "100%",
//                             overflow: "auto",
//                             display: "flex",
//                             flexDirection: "column",
//                           }}
//                         >
//                           <Box
//                             sx={{
//                               display: "flex",
//                               justifyContent: "space-between",
//                               alignItems: "center",
//                               mb: 2,
//                               pb: 1,
//                               borderBottom: "1px solid #e0e0e0",
//                               position: "sticky",
//                               top: 0,
//                               backgroundColor: "#f8f9fa",
//                               zIndex: 1,
//                             }}
//                           >
//                             <Typography
//                               variant="h6"
//                               sx={{
//                                 fontWeight: "600px",
//                                 color: "#0b0e14",
//                                 fontSize: "16px",
//                               }}
//                             >
//                               {stage.name}
//                             </Typography>
//                             <Typography
//                               variant="body2"
//                               sx={{
//                                 backgroundColor: "primary.main",
//                                 color: "white",
//                                 px: 1,
//                                 py: 0.5,
//                                 borderRadius: 1,
//                                 fontWeight: "bold",
//                               }}
//                             >
//                               {opportunities.length}
//                             </Typography>
//                           </Box>

//                           <Box
//                             sx={{
//                               flex: 1,
//                               overflow: "hidden",
//                               "&:hover": {
//                                 overflowY: "auto",
//                                 scrollbarWidth: "thin",
//                                 scrollBehavior: "smooth",
//                               },
//                             }}
//                           >
//                             {opportunities.length > 0 ? (
//                               opportunities.map((opportunity) => (
//                                 <OpportunityCard
//                                   key={opportunity.id}
//                                   opportunity={opportunity}
//                                 />
//                               ))
//                             ) : (
//                               <Typography
//                                 variant="body2"
//                                 sx={{
//                                   color: "gray",
//                                   textAlign: "center",
//                                   py: 3,
//                                 }}
//                               >
//                                 No opportunities
//                               </Typography>
//                             )}
//                           </Box>
//                         </Box>
//                       );
//                     })}

//                     {pipelineStages.length === 0 && (
//                       <Typography
//                         variant="h6"
//                         sx={{ textAlign: "center", mt: 4, width: "100%" }}
//                       >
//                         No pipeline stages available
//                       </Typography>
//                     )}
//                   </Box>
//                 ) : (
//                   // List view
//                   <Box
//                     sx={{
//                       width: "100%",
//                       maxWidth: { sm: "100%", md: "1200px" },
//                       pb: 4,
//                       height: "calc(100vh - 280px)",
//                       overflow: "auto",
//                     }}
//                   >
//                     <Typography
//                       variant="h6"
//                       sx={{ mb: 2, fontWeight: "medium" }}
//                     >
//                       {activeStage ? activeStage.name : "All Stages"} (
//                       {currentOpportunities.length} opportunities)
//                     </Typography>

//                     <Box
//                       sx={{ display: "flex", flexDirection: "column", gap: 2 }}
//                     >
//                       {currentOpportunities.length > 0 ? (
//                         currentOpportunities.map((opportunity) => (
//                           <OpportunityCard
//                             key={opportunity.id}
//                             opportunity={opportunity}
//                           />
//                         ))
//                       ) : (
//                         <Typography
//                           variant="body1"
//                           sx={{ color: "gray", textAlign: "center", py: 3 }}
//                         >
//                           No opportunities found{" "}
//                           {activeStage ? `in ${activeStage.name}` : ""}
//                         </Typography>
//                       )}
//                     </Box>
//                   </Box>
//                 )}

//                 {/* {allOpportunities.length >= totalOpportunities && allOpportunities.length > 0 && (
//                   <Alert severity="success" sx={{ mt: 2 }}>
//                     All {totalOpportunities} opportunities loaded and sorted by stage!
//                   </Alert>
//                 )} */}
//               </>
//             )}
//           </Stack>
//         </Box>
//       </Box>
//       {sortPopUp}
//       <Drawer
//         sx={{
//           "& .MuiPaper-root.MuiDrawer-paper  ": {
//             width: isSmallScreen ? "100%" : 400,
//           },
//         }}
//         anchor="right"
//         open={filter}
//         onClose={toggleFilterDrawer(false)}
//       >
//         {FilterDrawerList}
//       </Drawer>
//       <Drawer
//         anchor="right"
//         open={manageFieldsOpen}
//         onClose={toggleManageFieldsDrawer(false, "Backdrop/Close Button")}
//         sx={{
//           "& .MuiPaper-root.MuiDrawer-paper": {
//             width: isSmallScreen ? "100%" : 400,
//           },
//         }}
//       >
//         <ManageFieldsDrawer onClose={toggleManageFieldsDrawer(false)} />
//       </Drawer>
//     </AppTheme>
//   );
// }




import * as React from "react";
import { useParams } from "react-router-dom";
import { format } from "date-fns";
import { alpha } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import AppNavbar from "../dashboard/components/AppNavbar";
import Header from "../dashboard/components/Header";
import SideMenu from "../dashboard/components/SideMenu";
import { fetchOpportunities } from '../services/opportunities';
import { processOpportunitiesData } from '../services/opportunities';
import AppTheme from "../shared-theme/AppTheme";
import {
  // chartsCustomizations,
  dataGridCustomizations,
  datePickersCustomizations,
  treeViewCustomizations,
} from "../dashboard/theme/customizations/index";
import chartsCustomizations from '../dashboard/theme/customizations/charts'
import { useDispatch, useSelector } from "react-redux";
import { fetchLocations } from "../redux/actions/location";
import {
  Button,
  Typography,
  CircularProgress,
  Card,
  CardContent,
  Chip,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Drawer,
  IconButton,
  Divider,
  List,
  ListItemText,
  ListItemButton,
  ListItemIcon,
  useMediaQuery,
  Checkbox,
  Popover,
  Tooltip,
  TextField,
  InputAdornment,
  Avatar,
  Badge,
  RadioGroup,
  FormControlLabel,
  Radio,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  tooltipClasses,
} from "@mui/material";
import { locationservice } from "../services/locationservice";
import CloseIcon from "@mui/icons-material/Close";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import FilterAltOutlinedIcon from "@mui/icons-material/FilterAltOutlined";
import {
  mockFields,
  menuOptions,
  sortOptions,
  allFieldValues,
} from "../contant/mockData";
import SwapVertOutlinedIcon from "@mui/icons-material/SwapVertOutlined";
import BackspaceOutlinedIcon from "@mui/icons-material/BackspaceOutlined";
import SearchOutlinedIcon from "@mui/icons-material/SearchOutlined";
import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
import CallOutlinedIcon from "@mui/icons-material/CallOutlined";
import SmsOutlinedIcon from "@mui/icons-material/SmsOutlined";
import LocalOfferOutlinedIcon from "@mui/icons-material/LocalOfferOutlined";
import DescriptionOutlinedIcon from "@mui/icons-material/DescriptionOutlined";
import CheckBoxOutlinedIcon from "@mui/icons-material/CheckBoxOutlined";
import CalendarMonthOutlinedIcon from "@mui/icons-material/CalendarMonthOutlined";
import ExpandMoreOutlinedIcon from "@mui/icons-material/ExpandMoreOutlined";
import FilterListIcon from "@mui/icons-material/FilterList";
import ManageFieldsDrawer from "./ManageFieldsDrawer";
import Search from "../dashboard/components/Search";
import { useEffect } from "react";

const xThemeComponents = {
  ...chartsCustomizations,
  ...dataGridCustomizations,
  ...datePickersCustomizations,
  ...treeViewCustomizations,
};

export default function SubAccountDetail(props) {
  const { id } = useParams();
  const dispatch = useDispatch();
  const [pipelineStages, setPipelineStages] = React.useState([]);
  const [stageOpportunities, setStageOpportunities] = React.useState({});
  const [allOpportunities, setAllOpportunities] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);
  const [pipelineId, setPipelineId] = React.useState(null);
  const [pipelineName, setPipelineName] = React.useState("");
  const [totalOpportunities, setTotalOpportunities] = React.useState(0);
  const [viewMode, setViewMode] = React.useState("kanban"); // kanban or list
  const [activeStageId, setActiveStageId] = React.useState("");
  const [activeStage, setActiveStage] = React.useState(null);
  const [debugInfo, setDebugInfo] = React.useState(null); // For debugging

  const locationData = useSelector((state) => state.locations?.locations);

  React.useEffect(() => {
    dispatch(fetchLocations());
  }, [dispatch]);

  // API key for all API calls
  const ghlApiKey =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6Ikx3V3hmcWY4N2NYN1hQMldpcW1jIiwiY29tcGFueV9pZCI6IjA5bnRJc1JieDRnQXhjaGJIcWU3IiwidmVyc2lvbiI6MSwiaWF0IjoxNjU4ODMzNTgwODI1LCJzdWIiOiJ1c2VyX2lkIn0.xDo-pGLuK4QPjLDcy6sbXjKRjx3V3A-qO_X9TjUS_-Y";

  const fetchPipelineData = async () => {
    try {
      // console.log("Starting fetchPipelineData function");
      setLoading(true);
      setError(null);
      setAllOpportunities([]);
      setStageOpportunities({});
      setTotalOpportunities(0);
      setDebugInfo("Starting pipeline data fetch");

      // console.log("Fetching pipeline data with API key");
      const response = await locationservice.getPipeline();
      // console.log("Pipeline response:", response);
      setDebugInfo(`Pipeline response received: ${JSON.stringify(response)}`);

      if (response?.pipelines && response.pipelines.length > 0) {
        const pipeline = response.pipelines[0];
        setPipelineId(pipeline.id);
        setPipelineName(pipeline.name || "Pipeline");
        setDebugInfo(`Pipeline found: ${pipeline.name} (${pipeline.id})`);

        if (pipeline.stages && pipeline.stages.length > 0) {
          const sortedStages = [...pipeline.stages].sort(
            (a, b) => (a.position || 0) - (b.position || 0)
          );
          setPipelineStages(sortedStages);
          // console.log("Sorted stages:", sortedStages);
          setDebugInfo(`Found ${sortedStages.length} stages, fetching opportunities...`);

          // Use a small delay to ensure state updates before fetching opportunities
          setTimeout(() => {
            fetchAllOpportunities(pipeline.id, sortedStages);
          }, 100);
        } else {
          setError("No stages found in the pipeline");
          setDebugInfo("Error: No stages found in pipeline");
          setLoading(false);
        }
      } else {
        setError("No pipelines found");
        setDebugInfo("Error: No pipelines found in response");
        setLoading(false);
      }
    } catch (err) {
      console.error("Error fetching pipeline data:", err);
      setError(`Failed to fetch pipeline data: ${err.message}`);
      setDebugInfo(`Error in fetchPipelineData: ${err.message}`);
      setLoading(false);
    }
  };

  const fetchAllOpportunities = async (pipelineId, stages) => {
    try {
      setDebugInfo(`Fetching opportunities for pipeline: ${pipelineId}`);
      setLoading(true);

      const response = await locationservice.getAllPipelineOpportunities();

      if (!response || !response.success) {
        throw new Error('Failed to fetch opportunities');
      }

      // Process opportunities by stage
      const opportunitiesByStage = {};
      stages.forEach(stage => {
        opportunitiesByStage[stage.id] = [];
      });

      // Sort opportunities into their respective stages
      if (response.data && response.data.opportunities) {
        response.data.opportunities.forEach(opportunity => {
          const stageId = opportunity.stageId || opportunity.pipelineStageId;
          if (opportunitiesByStage[stageId]) {
            opportunitiesByStage[stageId].push(opportunity);
          }
        });
      }

      // Update state with all opportunities
      setStageOpportunities(opportunitiesByStage);
      setAllOpportunities(response.data?.opportunities || []);
      setTotalOpportunities(response.meta.total);
      setDebugInfo(`Successfully loaded ${response.meta.total} opportunities`);
      setLoading(false);

    } catch (err) {
      console.error("Error fetching all opportunities:", err);
      setError(`Failed to fetch opportunities: ${err.message}`);
      setDebugInfo(`Error in fetchAllOpportunities: ${err.message}`);
      setLoading(false);

      // Initialize empty opportunities by stage
      const emptyOpportunitiesByStage = {};
      stages.forEach((stage) => {
        emptyOpportunitiesByStage[stage.id] = [];
      });
      setStageOpportunities(emptyOpportunitiesByStage);
    }
  };

  // Toggle view mode between kanban and list
  const toggleViewMode = () => {
    setViewMode(viewMode === "kanban" ? "list" : "kanban");
  };

  // Handle stage selection change
  const handleStageChange = (event) => {
    const stageId = event.target.value;
    setActiveStageId(stageId);

    // Find the selected stage object
    if (stageId) {
      const stage = pipelineStages.find((s) => s.id === stageId);
      setActiveStage(stage);
    } else {
      setActiveStage(null);
    }
  };

  // Initial data fetch
  React.useEffect(() => {
    //    console.log("SubAccountDetail component mounted with ID:", id);
    setDebugInfo(`Component mounted with ID: ${id}`);
    fetchPipelineData();
  }, [id]);

  // Opportunity card component
  const OpportunityCard = ({ opportunity }) => {
    return (
      <Card sx={{
        mb: 2,
        boxShadow: 1,
        marginRight: "5px",
        backgroundColor: (theme) => theme.palette.background.paper
      }}>
        <CardContent sx={{ px: 2, "&:last-child": { pb: 2 } }}>
          <Box sx={{ textAlign: "right" }}>
            {opportunity.status && (
              <Chip
                label={opportunity.status}
                size="small"
                color={
                  opportunity.status === "open"
                    ? "primary"
                    : opportunity.status === "won"
                      ? "success"
                      : "default"
                }
                variant="outlined"
                sx={{ fontSize: "0.7rem", height: "20px" }}
              />
            )}
          </Box>
          {/* <Typography variant="subtitle1" fontWeight="600" color="#0b0e14"> */}
          <Typography variant="subtitle1" fontWeight="600" sx={{ color: (theme) => theme.palette.text.primary, }}>
            {opportunity.name || "Untitled Opportunity"}
          </Typography>

          {(opportunity.monetaryValue > 0 || opportunity.value > 0) && (
            <>
              <Box sx={{ display: "flex", gap: "10px", alignItems: "center" }}>
                <span
                  style={{
                    color: "#607179",
                    fontWeight: "600",
                    mt: 1,
                    fontSize: "0.75rem",
                  }}
                >
                  Opportunity Value:
                </span>
                <Typography
                  variant="body2"
                  sx={{ color: "success.main", fontWeight: "bold" }}
                >
                  $
                  {parseFloat(
                    opportunity.monetaryValue || opportunity.value
                  ).toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </Typography>
              </Box>
            </>
          )}

          {opportunity.source && (
            <Typography
              variant="body2"
              sx={{ mt: 1, fontSize: "0.75rem" }}
            >
              <span style={{ color: "#607179", fontWeight: "600" }}>
                Opportunity Source:
              </span>{" "}
              {opportunity.source}
            </Typography>
          )}
          <Box
            mt={3}
            display="flex"
            alignItems="center"
            gap={1.5}
            flexWrap="wrap"
          >
            <Tooltip title="CALL" arrow>
              <CallOutlinedIcon sx={{ width: 16, height: 16 }} />
            </Tooltip>

            <Tooltip title="View Conversation" arrow>
              <SmsOutlinedIcon sx={{ width: 16, height: 16 }} />
            </Tooltip>

            <Tooltip title="Add Tags" arrow>
              <LocalOfferOutlinedIcon sx={{ width: 16, height: 16 }} />
            </Tooltip>

            <Tooltip title="Add Note" arrow>
              <DescriptionOutlinedIcon sx={{ width: 16, height: 16 }} />
            </Tooltip>

            <Tooltip title="Add Task" arrow>
              <CheckBoxOutlinedIcon sx={{ width: 16, height: 16 }} />
            </Tooltip>
            <Tooltip title="Add Appointment" arrow>
              <CalendarMonthOutlinedIcon sx={{ width: 16, height: 16 }} />
            </Tooltip>
          </Box>
        </CardContent>
      </Card>
    );
  };

  // Count opportunities per stage
  const countByStage = React.useMemo(() => {
    const counts = {};

    // Add count for each stage
    pipelineStages.forEach((stage) => {
      counts[stage.id] = stageOpportunities[stage.id]?.length || 0;
    });

    // Add total count
    counts.all = allOpportunities.length;

    return counts;
  }, [pipelineStages, stageOpportunities, allOpportunities]);

  // Get current filtered opportunities
  const currentOpportunities = React.useMemo(() => {
    if (!activeStageId) {
      return allOpportunities;
    }
    return stageOpportunities[activeStageId] || [];
  }, [activeStageId, allOpportunities, stageOpportunities]);

  const [filter, setFilterOpen] = React.useState(false);
  const [activeFilterMenu, setActiveFilterMenu] = React.useState(null); // null = show fields list
  const [selectedFilterOptions, setSelectedFilterOptions] = React.useState({});
  const isSmallScreen = useMediaQuery("(max-width:600px)");

  const toggleFilterDrawer = (newOpen) => () => setFilterOpen(newOpen);

  const handleFieldClick = (fieldId) => setActiveFilterMenu(fieldId);

  const handleBack = () => setActiveFilterMenu(null);

  const handleFilterCheckboxChange = (fieldId, optionId) => {
    setSelectedFilterOptions((prev) => {
      const current = prev[fieldId] || [];
      const updated = current.includes(optionId)
        ? current.filter((id) => id !== optionId)
        : [...current, optionId];
      return { ...prev, [fieldId]: updated };
    });
  };

  const FilterDrawerList = (
    <Box
      role="presentation"
      display="flex"
      flexDirection="column"
      height="100%"
    >
      <Box px={3} py={2} flexShrink={0}>
        <Stack direction="row" justifyContent="space-between">
          <Box>
            <Typography variant="h4">Filters</Typography>
            <Typography variant="body2" color="text.disabled">
              Apply filters to opportunities
            </Typography>
          </Box>
          <Box>
            <IconButton onClick={toggleFilterDrawer(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Stack>
      </Box>

      <Divider />
      <Box flexGrow={1} overflow="auto" sx={{ px: 2, pt: 2 }}>
        {!activeFilterMenu && (
          <List>
            {mockFields.map((field) => (
              <ListItemButton
                key={field.id}
                sx={{
                  mb: "15px",
                  display: "flex",
                  justifyContent: "space-between",
                  borderRadius: "8px",
                  backgroundColor: (theme) => theme.palette.mode === 'dark'
                    ? theme.palette.grey[800]
                    : "#f2f4f7",
                }}
                onClick={() => handleFieldClick(field.id)}
              >
                <ListItemText primary={field.label} />
                <ListItemIcon>
                  <ChevronRightIcon />
                </ListItemIcon>
              </ListItemButton>
            ))}
          </List>
        )}

        {activeFilterMenu && (
          <Box>
            <Button
              startIcon={<ArrowBackIosIcon />}
              onClick={handleBack}
              sx={{ mb: 1, textTransform: "none" }}
            >
              Back
            </Button>

            <Typography variant="h6" mb={2}>
              {mockFields.find((f) => f.id === activeFilterMenu)?.label}
            </Typography>

            <List>
              {(menuOptions[activeFilterMenu] || []).map((option) => (
                <ListItemButton
                  key={option.id}
                  onClick={() =>
                    handleFilterCheckboxChange(activeFilterMenu, option.id)
                  }
                >
                  <Checkbox
                    edge="start"
                    checked={(
                      selectedFilterOptions[activeFilterMenu] || []
                    ).includes(option.id)}
                  />
                  <ListItemText primary={option.label} />
                </ListItemButton>
              ))}
            </List>
          </Box>
        )}
      </Box>

      <Box px={3} py={2} flexShrink={0}>
        <Stack direction="row" gap={1} justifyContent="end">
          <Button variant="outlined" onClick={toggleFilterDrawer(false)}>
            Cancel
          </Button>
          <Button variant="contained">Apply</Button>
        </Stack>
      </Box>
    </Box>
  );

  const [anchorEl, setAnchorEl] = React.useState(null);
  const [sortOption, setSortOption] = React.useState("");
  const openSort = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleClear = () => {
    setSortOption("");
  };

  const handleChange = (event) => {
    setSortOption(event.target.value);
  };

  const open = Boolean(anchorEl);
  const popId = open ? "sort-popover" : undefined;

  const sortPopUp = (
    <Popover
      id={popId}
      open={open}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      sx={{ mt: 1 }}
    >
      {/* Header with Title & Clear Button */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        px={2}
        py={1.5}
        minWidth={280}
      >
        <Typography variant="subtitle1" fontWeight={600}>
          Sort By
        </Typography>
        <Button color="info" size="small" onClick={handleClear}>
          Clear
        </Button>
      </Box>

      {/* Select Dropdown with Fixed Height & Tooltip */}
      <Box px={2} pb={2}>
        <FormControl fullWidth size="small">
          <InputLabel
          sx={{
            '&.Mui-focused': {
              // backgroundColor: '#fff',
              backgroundColor: (theme) => theme.palette.background.paper,
              px: 0.5
            }
          }}
          >
            Sort By
          </InputLabel>
          <Select
            value={sortOption}
            onChange={handleChange}
            MenuProps={{
              PaperProps: { sx: { maxHeight: 250 } }, // Fixed height with scroll
            }}
            sx={{ width: "100%" }} // Ensure full width
          >
            {sortOptions.map((option) => (
              <MenuItem
                key={option.value}
                value={option.value}
                sx={{
                  maxWidth: 300,
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
    </Popover >
  );

  const [manageFieldsOpen, setManageFieldsOpen] = React.useState(false);
  const toggleManageFieldsDrawer = (newOpen) => () => {
    // console.log("Drawer toggled:", newOpen ? "Opened" : "Closed");
    setManageFieldsOpen(newOpen);
  };

  const locationId = props.subAccount?.id || props.locationId;
  const locationAccessToken = props.subAccount?.accessToken || props.locationAccessToken;

  const [pipelines, setPipelines] = React.useState([]);
  const [pipelineIds, setPipelineIds] = React.useState([]);
  const [opportunitiesByPipeline, setOpportunitiesByPipeline] = React.useState([]);
  const [isLoading, setIsLoading] = React.useState(false);

  // Fetch pipelines first (if needed)
  React.useEffect(() => {
    const fetchPipelines = async () => {
      try {
        // Your existing pipeline fetching code
        // const pipelinesData = await fetchPipelines(locationId, locationAccessToken);
        // setPipelines(pipelinesData);
        // Extract just the IDs for the opportunities API call
        // const ids = pipelinesData.map(pipeline => pipeline.id);
        // setPipelineIds(ids);

        // For demonstration purposes:
        const mockPipelines = [
          { id: 'pipeline1', name: 'Sales Pipeline' },
          { id: 'pipeline2', name: 'Marketing Pipeline' }
        ];
        setPipelines(mockPipelines);
        setPipelineIds(mockPipelines.map(p => p.id));
      } catch (error) {
        console.error('Error fetching pipelines:', error);
        setError('Failed to load pipelines');
      }
    };

    if (locationId && locationAccessToken) {
      fetchPipelines();
    }
  }, [locationId, locationAccessToken]);

  // Load opportunities once we have pipeline IDs
  const loadOpportunities = async () => {
    if (!locationId || !locationAccessToken || pipelineIds.length === 0) {
      console.warn('Missing required data to fetch opportunities');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 1. Fetch raw data from API
      const rawData = await fetchOpportunities(locationId, pipelineIds, locationAccessToken);

      if (!rawData) {
        setError('Failed to fetch opportunities data');
        return;
      }

      // 2. Process the data with our pipelines information
      const organizedData = processOpportunitiesData(rawData, pipelines);

      // 3. Update your component state
      setOpportunitiesByPipeline(organizedData);

      // Optional: Log the final structure
      console.log("Final organized opportunities:", organizedData);
    } catch (error) {
      console.error('Error loading opportunities:', error);
      setError('Failed to load opportunities');
    } finally {
      setIsLoading(false);
    }
  };

  // Load opportunities when pipeline IDs are available
  useEffect(() => {
    if (pipelineIds.length > 0) {
      loadOpportunities();
    }
  }, [pipelineIds]); // Or add other dependencies as needed

  // const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  // const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <AppTheme {...props} themeComponents={xThemeComponents}>
      <CssBaseline enableColorScheme />
      <Box sx={{ display: "flex" }}>
        <SideMenu />
        <AppNavbar />
        <Box
          component="main"
          sx={(theme) => ({
            flexGrow: 1,
            backgroundColor: theme.vars
              ? `rgba(${theme.vars.palette.background.defaultChannel} / 1)`
              : alpha(theme.palette.background.default, 1),
            overflow: "auto",
            height: "100vh",
          })}
        >
          <Header title="Sub-Account-Detail" />
          <Stack
            spacing={2}
            sx={{
              alignItems: "center",
              padding: "1rem 1rem 0rem 1rem",
              mt: { xs: 8, md: 0 },
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                width: "100%",
                flexWrap: "wrap",
              }}
            >
              <Typography variant="h5" sx={{
                fontWeight: "bold",
                color: (theme) => theme.palette.text.primary
              }}>
                {pipelineName}
              </Typography>
              {/* <Typography variant="body2" color="text.secondary">
                Account ID: {locationId}
              </Typography> */}
            </Box>

            {error && (
              <Alert severity="error" sx={{ width: "100%" }}>
                {error}
              </Alert>
            )}

            {/* Controls and filters */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                margin: "auto",
                width: "100%",
                flexWrap: "wrap",
                gap: 1,
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <FormControl size="small" sx={{
                  // minWidth: 200,
                  minWidth: isSmallScreen ? '100%' : '200px', // Adjust width as needed
                }}>
                  <InputLabel id="demo-simple-select-label"
                    sx={{
                      '&.Mui-focused': {
                        // backgroundColor: '#fff',
                        backgroundColor: (theme) => theme.palette.background.paper,
                        px: 0.5
                      }
                    }}>Filter by Stage</InputLabel>
                  <Select
                    value={activeStageId}
                    labelId="demo-simple-select-label"
                    label="Filter by Stage"
                    id='filterByState'
                    onChange={handleStageChange}
                  >
                    <MenuItem value="">
                      All Stages ({countByStage.all || 0})
                    </MenuItem>
                    {pipelineStages.map((stage) => (
                      <MenuItem key={stage.id} value={stage.id}>
                        {stage.name} ({countByStage[stage.id] || 0})
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* <Button
                  size="small"
                  variant="outlined"
                  onClick={toggleViewMode}
                  sx={{ ml: 1 }}
                >
                  {viewMode === "kanban" ? "List View" : "Kanban View"}
                </Button> */}
                {/* <Button
                  variant="outlined"
                  startIcon={<FilterListIcon />}
                  onClick={handleClick}
                >
                  Sort
                </Button> */}

                {/* <Popover
                  open={openSort}
                  anchorEl={anchorEl}
                  onClose={handleClose}
                  anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "left",
                  }}
                  transformOrigin={{
                    vertical: "top",
                    horizontal: "left",
                  }}
                >
                  <Box sx={{ p: 2, minWidth: 250 }}>
                    <Typography
                      variant="subtitle1"
                      sx={{ fontWeight: "bold", mb: 1 }}
                    >
                      Sort By
                    </Typography>
                    <FormControl fullWidth size="small">
                      <InputLabel>Sort By</InputLabel>
                      <Select
                        value={props.activeSort}
                        onChange={(e) => {
                          // handleSortChange(e);
                          handleClose();
                        }}
                      >
                        {sortOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                </Popover> */}

                {/* <Search width="45ch" /> */}
                <TextField
                  size="small"
                  placeholder="Search Pipelines"
                  variant="outlined"
                  sx={{
                    width: isSmallScreen ? '100%' : '200px', // Adjust width as needed
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "8px",
                      backgroundColor: (theme) => theme.palette.background.paper,
                    },
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchOutlinedIcon color="disabled" />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
              {/* <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <FormControl size="small" sx={{ minWidth: 200 }}>
                  <InputLabel>Filter by Stage</InputLabel>
                  <Select
                    value={activeStageId}
                    label="Filter by Stage"
                    onChange={handleStageChange}
                  >
                    <MenuItem value="">
                      All Stages ({countByStage.all || 0})
                    </MenuItem>
                    {pipelineStages.map((stage) => (
                      <MenuItem key={stage.id} value={stage.id}>
                        {stage.name} ({countByStage[stage.id] || 0})
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <Button
                  size="small"
                  variant="outlined"
                  onClick={toggleViewMode}
                  sx={{ ml: 1 }}
                >
                  {viewMode === "kanban" ? "List View" : "Kanban View"}
                </Button>
              </Box> */}

              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                {allOpportunities.length > 0 && (
                  <Typography variant="body2" color="text.secondary">
                    Loaded {allOpportunities.length} of {totalOpportunities}{" "}
                    opportunities
                  </Typography>
                )}

                <Button
                  variant="contained"
                  color="primary"
                  onClick={fetchPipelineData}
                  disabled={loading}
                  sx={{
                    backgroundColor: (theme) => theme.palette.primary.main,
                    color: (theme) => theme.palette.primary.contrastText,
                    '&:hover': {
                      backgroundColor: (theme) => theme.palette.primary.dark,
                    },
                    '&:disabled': {
                      backgroundColor: (theme) => theme.palette.action.disabledBackground,
                      color: (theme) => theme.palette.action.disabled,
                    }
                  }}
                >
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    "Refresh Data"
                  )}
                </Button>
              </Box>
            </Box>
            <Stack
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                // direction: "",
                flexDirection: "row",
                width: "100%",
                flexWrap: "wrap",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  flexWrap: "wrap",
                  gap: 2,
                }}
              >
                <Chip
                  icon={<FilterAltOutlinedIcon />}
                  label="Advanced Filters"
                  size={isSmallScreen ? "small" : "medium"}
                  variant="outlined"
                  onClick={toggleFilterDrawer(true)}
                />
                <Chip
                  icon={<SwapVertOutlinedIcon />}
                  label={`Sort ${sortOption ? "(1)" : ""}`}
                  size="medium"
                  variant="outlined"
                  onClick={handleClick}
                  aria-describedby={popId}
                />
              </Box>

              <Box display="flex" alignItems="center" gap={2}>
                {/* Search Bar */}
                <TextField
                  size="small"
                  placeholder="Search Opportunities"
                  variant="outlined"
                  sx={{
                    width: 250, // Adjust width as needed
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "8px",
                      backgroundColor: (theme) => theme.palette.background.paper,
                    },
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchOutlinedIcon color="disabled" />
                      </InputAdornment>
                    ),
                  }}
                />

                {/* Manage Fields Button */}
                <Button
                  variant="outlined"
                  onClick={toggleManageFieldsDrawer(true, "Button Click")}
                >
                  <SettingsOutlinedIcon fontSize="small" />
                  <Typography variant="body2" fontWeight={500} sx={{ ml: 0.5 }}>
                    Manage Fields
                  </Typography>
                </Button>
              </Box>
            </Stack>

            {loading ? (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  my: 4,
                }}
              >
                <CircularProgress />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Loading all opportunities... This may take a moment.
                </Typography>
              </Box>
            ) : (
              <>
                {viewMode === "kanban" ? (
                  <Box
                    sx={{
                      display: "flex",
                      gap: 2,
                      padding: "16px 0px",
                      overflowX: "hidden",
                      width: "100%",
                      maxWidth: { sm: "auto", md: "1700px" },
                      pb: 4,
                      height: "calc(100vh - 280px)",
                      "&:hover": {
                        overflowX: "auto",
                        scrollbarWidth: "thin",
                      },
                    }}
                  >
                    {pipelineStages.map((stage) => {
                      const opportunities = stageOpportunities[stage.id] || [];
                      console.log("Stage Opportunities:", stageOpportunities);
                      console.log(`Fetching opportunities for stage ${stage.name} (ID: ${stage.id})`);
                      console.log("Opportunities for this stage:", opportunities);


                      return (
                        <Box
                          key={stage.id}
                          sx={{
                            minWidth: "340px",
                            maxWidth: "400px",
                            backgroundColor: (theme) => theme.palette.mode === 'dark'
                              ? theme.palette.grey[900]
                              : "#f8f9fa",
                            borderRadius: 2,
                            padding: 2,
                            boxShadow: 2,
                            height: "fit-content",
                            maxHeight: "100%",
                            overflow: "auto",
                            display: "flex",
                            flexDirection: "column",
                          }}
                        >
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              mb: 2,
                              pb: 1,
                              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                              position: "sticky",
                              top: 0,
                              backgroundColor: (theme) => theme.palette.mode === 'dark'
                                ? theme.palette.grey[900]
                                : "#f8f9fa",
                              zIndex: 1,
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{
                                fontWeight: "600px",
                                color: (theme) => theme.palette.text.primary,
                                fontSize: "16px",
                              }}
                            >
                              {stage.name}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{
                                backgroundColor: "primary.main",
                                color: "white",
                                px: 1,
                                py: 0.5,
                                borderRadius: 1,
                                fontWeight: "bold",
                              }}
                            >
                              {opportunities.length}
                            </Typography>
                          </Box>

                          <Box
                            sx={{
                              flex: 1,
                              overflow: "hidden",
                              "&:hover": {
                                overflowY: "auto",
                                scrollbarWidth: "thin",
                                scrollBehavior: "smooth",
                              },
                            }}
                          >
                            {opportunities.length > 0 ? (
                              opportunities.map((opportunity) => (
                                <OpportunityCard
                                  key={opportunity.id}
                                  opportunity={opportunity}
                                />
                              ))
                            ) : (
                              <Typography
                                variant="body2"
                                sx={{
                                  color: (theme) => theme.palette.text.secondary,
                                  textAlign: "center",
                                  py: 3,
                                }}
                              >
                                No opportunities
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      );
                    })}

                    {pipelineStages.length === 0 && (
                      <Typography
                        variant="h6"
                        sx={{ textAlign: "center", mt: 4, width: "100%" }}
                      >
                        No pipeline stages available
                      </Typography>
                    )}
                  </Box>
                ) : (
                  // List view
                  <Box
                    sx={{
                      width: "100%",
                      maxWidth: { sm: "100%", md: "1200px" },
                      pb: 4,
                      height: "calc(100vh - 280px)",
                      overflow: "auto",
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{ mb: 2, fontWeight: "medium" }}
                    >
                      {activeStage ? activeStage.name : "All Stages"} (
                      {currentOpportunities.length} opportunities)
                    </Typography>

                    <Box
                      sx={{ display: "flex", flexDirection: "column", gap: 2 }}
                    >
                      {currentOpportunities.length > 0 ? (
                        currentOpportunities.map((opportunity) => (
                          <OpportunityCard
                            key={opportunity.id}
                            opportunity={opportunity}
                          />
                        ))
                      ) : (
                        <Typography
                          variant="body1"
                          sx={{
                            color: (theme) => theme.palette.text.secondary,
                            textAlign: "center",
                            py: 3
                          }}
                        >
                          No opportunities found{" "}
                          {activeStage ? `in ${activeStage.name}` : ""}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                )}

                {/* {allOpportunities.length >= totalOpportunities && allOpportunities.length > 0 && (
                  <Alert severity="success" sx={{ mt: 2 }}>
                    All {totalOpportunities} opportunities loaded and sorted by stage!
                  </Alert>
                )} */}
              </>
            )}
          </Stack>
        </Box>
      </Box>
      {sortPopUp}
      <Drawer
        sx={{
          "& .MuiPaper-root.MuiDrawer-paper  ": {
            width: isSmallScreen ? "100%" : 400,
          },
        }}
        anchor="right"
        open={filter}
        onClose={toggleFilterDrawer(false)}
      >
        {FilterDrawerList}
      </Drawer>
      <Drawer
        anchor="right"
        open={manageFieldsOpen}
        onClose={toggleManageFieldsDrawer(false, "Backdrop/Close Button")}
        sx={{
          "& .MuiPaper-root.MuiDrawer-paper": {
            width: isSmallScreen ? "100%" : 400,
          },
        }}
      >
        <ManageFieldsDrawer onClose={toggleManageFieldsDrawer(false)} />
      </Drawer>
    </AppTheme>
  );
}

import axios from "axios";
const base_url = process.env.REACT_APP_API_BASE_URL;
const NEW_API_URL = process.env.REACT_APP_API_BASE_URL;
// const NEW_API_URL = 'http://************:5000';

export const locationservice = {
  getPipeline,
  getPipelineOpportunities,
  getAllPipelineOpportunities, // New function to get all opportunities at once
  getLocationsKey,
};

async function getPipeline() {
  const locationId = window.location.pathname.split("/")[1];
  const rawCompanyId = localStorage.getItem("companyId");

  if (!rawCompanyId) {
    console.error("Company ID not found!");
    return;
  }

  const companyId = JSON.parse(rawCompanyId);
  const ghlAccessToken = localStorage.getItem("ghlAccessToken");
  const ghlaccessToken = localStorage.getItem('ghlaccessToken');

  let agencyToken = ghlAccessToken;
    if (!agencyToken || agencyToken === 'undefined' || agencyToken === 'null' || agencyToken === '') {
      // If ghlAccessToken is not valid, use ghlaccessToken
      agencyToken = ghlaccessToken;
    }

  try {
    const res = await fetch(`${NEW_API_URL}/api/get-location-access-token/`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${agencyToken}`,
      },
      body: JSON.stringify({
        locationId: locationId,
        companyId: companyId,
      }),
    });

    console.log('/api/get-location-access-token/',res)

    if (res.status === 200) {
      const data = await res.json();
      localStorage.setItem("locationAccessToken", data.access_token);

      const locationAccessToken = data.access_token;

      // Now fetch pipelines
      const pipelineRes = await fetch(
        `${NEW_API_URL}/api/pipelines?locationId=${locationId}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${locationAccessToken}`,
          },
        }
      );

      const pipelineData = await pipelineRes.json();
      console.log("Pipelines:", pipelineData);
      return pipelineData;
    } else {
      console.error("Failed to get location access token. Status:", res.status);
    }
  } catch (error) {
    console.error("Error in getPipeline:", error);
  }
}

async function getPipelineOpportunities(
  pipelineId,
  startAfterId,
  startAfter,
  limit = 100,
  ghlApiKey
) {
  try {
    // Log request details for debugging

    const response = await axios.get(
      `${base_url}/api/pipelines/${pipelineId}/opportunities`,
      {
        params: {
          startAfterId,
          startAfter,
          limit,
        },
        headers: {
          Authorization: `Bearer ${ghlApiKey}`,
          "Content-Type": "application/json",
        },
      }
    );

    // Ensure consistent stageId field on all opportunities
    if (response.data && response.data.opportunities) {
      response.data.opportunities = response.data.opportunities.map((opp) => {
        // The API sometimes uses stageId and sometimes pipelineStageId
        const stageId = opp.stageId || opp.pipelineStageId;
        return {
          ...opp,
          stageId,
          pipelineStageId: stageId, // Ensure both fields exist for consistency
        };
      });
    }

    return response.data;
  } catch (error) {
    // Enhanced error logging
    console.error("Error fetching opportunities:", error.message);

    if (error.response) {
      console.error(`Response status: ${error.response.status}`);
      console.error(`Response data:`, error.response.data);
    }

    // Return a structured empty response instead of null
    return {
      opportunities: [],
      meta: { total: 0 },
    };
  }
}

// New function to get ALL opportunities at once
// async function getAllPipelineOpportunities(pipelineId, ghlApiKey) {
//   try {
//     // This calls our new custom backend endpoint that fetches all pages
//     const response = await axios.get(
//       `${base_url}/api/pipelines/${pipelineId}/all-opportunities`,
//       {
//         headers: {
//           Authorization: `Bearer ${ghlApiKey}`,
//           "Content-Type": "application/json",
//         },
//       }
//     );

//     // Ensure consistent stageId field on all opportunities
//     if (response.data && response.data.opportunities) {
//       response.data.opportunities = response.data.opportunities.map((opp) => {
//         // The API sometimes uses stageId and sometimes pipelineStageId
//         const stageId = opp.stageId || opp.pipelineStageId;
//         return {
//           ...opp,
//           stageId,
//           pipelineStageId: stageId, // Ensure both fields exist for consistency
//           pipelineId: pipelineId,
//         };
//       });
//     }

//     return response.data;
//   } catch (error) {
//     // Enhanced error logging
//     console.error("Error fetching ALL opportunities:", error.message);

//     if (error.response) {
//       console.error(`Response status: ${error.response.status}`);
//       console.error(`Response data:`, error.response.data);
//     }

//     // Return a structured empty response instead of null
//     return {
//       opportunities: [],
//       meta: {
//         total: 0,
//         fetched: 0,
//       },
//     };
//   }
// }

// async function getAllPipelineOpportunities(pipelineId, ghlApiKey) {
//   const locationId = window.location.pathname.split("/").pop();
//   const rawCompanyId = localStorage.getItem("companyId");
//   const agencyToken = localStorage.getItem("ghlAccessToken");

//   if (!rawCompanyId || !agencyToken || !locationId) {
//     console.error("Missing required values!");
//     return null; // ✅ This return is now valid — it's inside the function
//   }

//   const companyId = JSON.parse(rawCompanyId);

//   try {
//     // Step 1: Get Location Access Token
//     const tokenRes = await fetch(
//       `${NEW_API_URL}/api/get-location-access-token/`,
//       {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//           Authorization: `Bearer ${agencyToken}`,
//         },
//         body: JSON.stringify({ locationId, companyId }),
//       }
//     );

//     if (!tokenRes.ok) {
//       console.error("Failed to get location access token");
//       return;
//     }

//     const tokenData = await tokenRes.json();
//     const locationAccessToken = tokenData.access_token;
//     localStorage.setItem("locationAccessToken", locationAccessToken);

//     // Step 2: Get Pipelines
//     const pipelineRes = await fetch(
//       `${NEW_API_URL}/api/pipelines?locationId=${locationId}`,
//       {
//         method: "GET",
//         headers: {
//           Authorization: `Bearer ${locationAccessToken}`,
//         },
//       }
//     );

//     const pipelineData = await pipelineRes.json();
//     const pipelineIds = pipelineData?.pipelines?.length
//       ? [pipelineData.pipelines[0].id]
//       : [];

//     if (pipelineIds.length === 0) {
//       console.warn("No pipelines found for this location.");
//       return;
//     }

//     // Step 3: Fetch Opportunities Pipeline-wise
//     const pipelineIdsForOpertunities = pipelineData?.pipelines?.map((p) => p.id) || [];

//     if (pipelineIdsForOpertunities.length === 0) {
//       console.warn("No pipelines found for this location.");
//       return;
//     }

//     const opportunitiesRes = await fetch(
//       `${NEW_API_URL}/api/opportunities/search?locationId=${locationId}&pipelineIds=${pipelineIdsForOpertunities.join(
//         ","
//       )}`,
//       {
//         method: "GET",
//         headers: {
//           Authorization: `Bearer ${locationAccessToken}`,
//         },
//       }
//     );

//     const opportunitiesData = await opportunitiesRes.json();
//     console.log("Grouped Opportunities by Pipeline:", opportunitiesData);

//     return opportunitiesData, pipelineData;
//   } catch (error) {
//     console.error("Full flow error:", error);
//   }
// }

// function getAllPipelineOpportunities() {
//   return (async () => {
//     const locationId = window.location.pathname.split("/").pop();
//     const rawCompanyId = localStorage.getItem("companyId");
//     const ghlAccessToken = localStorage.getItem("ghlAccessToken");
//     const ghlaccessToken = localStorage.getItem('ghlaccessToken');
  
//     let agencyToken = ghlAccessToken;
//       if (!agencyToken || agencyToken === 'undefined' || agencyToken === 'null' || agencyToken === '') {
//         // If ghlAccessToken is not valid, use ghlaccessToken
//         agencyToken = ghlaccessToken;
//       }

//     if (!rawCompanyId || !agencyToken || !locationId) {
//       console.error("Missing required values!");
//       return null;
//     }

//     const companyId = JSON.parse(rawCompanyId);

//     try {
//       // Get Location Access Token
//       const tokenRes = await fetch(`${NEW_API_URL}/api/get-location-access-token/`, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//           Authorization: `Bearer ${agencyToken}`,
//         },
//         body: JSON.stringify({ locationId, companyId }),
//       });

//       if (!tokenRes.ok) {
//         console.error("Failed to get location access token");
//         return;
//       }

//       const tokenData = await tokenRes.json();
//       const locationAccessToken = localStorage.getItem("locationAccessToken") || tokenData.access_token;
//       localStorage.setItem("locationAccessToken", locationAccessToken);

//       // Get Pipelines
//       const pipelineRes = await fetch(`${NEW_API_URL}/api/pipelines?locationId=${locationId}`, {
//         method: "GET",
//         headers: {
//           Authorization: `Bearer ${locationAccessToken}`,
//         },
//       });

//       const pipelineData = await pipelineRes.json();
//       const pipelines = pipelineData?.pipelines || [];

//       const pipelineIds = pipelines.map((p) => p.id);

//       if (pipelineIds.length === 0) {
//         console.warn("No pipelines found for this location.");
//         return;
//       }

//       // Fetch Opportunities with stages
//       const opportunitiesRes = await fetch(
//         `${NEW_API_URL}/api/opportunities/search?location_id=${locationId}&pipelineIds=${pipelineIds.join(",")}`,
//         {
//           method: "GET",
//           headers: {
//             Authorization: `Bearer ${locationAccessToken}`,
//           },
//         }
//       );

//       if (!opportunitiesRes.ok) {
//         console.error("Failed to fetch opportunities");
//         return;
//       }

//       // const response = await opportunitiesRes.json();
//       const data = await opportunitiesRes.json(); // <- This is important
//       console.log("opportunities response data:", data);

//       // No need to check data.success, instead you can work with data directly
//       // For example:
//       if (!data || !data.data) {
//         console.error("Unexpected opportunities response format");
//         return;
//       }
//       if (!data || !Array.isArray(data.data)) {
//         console.error("Unexpected opportunities response format", data);
//         return;
//       }

//       // // Now safely use the data
//       // const opportunities = data?.data || [];
//       // Process and organize the data
//       const organizedData = data?.data.map(pipelineData => {
//         const pipeline = pipelines.find(p => p.id === pipelineData.pipelineId);
//         return {
//           ...pipelineData,
//           pipelineName: pipeline?.name || pipelineData.pipelineName,
//           stages: Array.isArray(pipelineData.stages)
//             ? pipelineData.stages.map(stage => ({
//               ...stage,
//               opportunities: Array.isArray(stage.opportunities)
//                 ? stage.opportunities.map(opportunity => ({
//                   ...opportunity,
//                   pipelineName: pipeline?.name,
//                   stageName: stage.stageName
//                 }))
//                 : []
//             }))
//             : []
//         };
//       });


//       // Log the organized data structure
//       console.log("Organized Pipeline Data:", organizedData);

//       // Display opportunities in console (for debugging)
//       organizedData.forEach(pipeline => {
//         console.log(`Pipeline: ${pipeline}`);

//         pipeline.stages.forEach(stage => {
//           console.log(`Stage: ${stage.stageName}`);
//           console.log(`Number of opportunities: ${stage.opportunities.length}`);

//           stage.opportunities.forEach(opportunity => {
//             console.log(`- ${opportunity.name}`);
//           });
//         });
//       });

//       return {
//         pipelines,
//         opportunities: organizedData
//       };
//     } catch (error) {
//       console.error("Full flow error:", error);
//       throw error;
//     }
//   })();
// }

async function getAllPipelineOpportunities() {
  const locationId = window.location.pathname.split("/")[1];
  const rawCompanyId = localStorage.getItem("companyId");
  const ghlAccessToken = localStorage.getItem("ghlAccessToken");
  const ghlaccessToken = localStorage.getItem("ghlaccessToken");

  let agencyToken = ghlAccessToken;
  if (!agencyToken || agencyToken === 'undefined' || agencyToken === 'null' || agencyToken === '') {
    agencyToken = ghlaccessToken;
  }

  if (!rawCompanyId || !agencyToken || !locationId) {
    console.error("Missing required values!");
    return null;
  }

  const companyId = JSON.parse(rawCompanyId);

  try {
    // Get Location Access Token
    const tokenRes = await fetch(`${NEW_API_URL}/api/get-location-access-token/`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${agencyToken}`,
      },
      body: JSON.stringify({ locationId, companyId }),
    });

    if (!tokenRes.ok) {
      console.error("Failed to get location access token");
      return;
    }

    const tokenData = await tokenRes.json();
    const locationAccessToken = localStorage.getItem("locationAccessToken") || tokenData.access_token;
    localStorage.setItem("locationAccessToken", locationAccessToken);

    // Get Pipelines
    const pipelineRes = await fetch(`${NEW_API_URL}/api/pipelines?locationId=${locationId}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${locationAccessToken}`,
      },
    });

    const pipelineData = await pipelineRes.json();
    const pipelines = pipelineData?.pipelines || [];

    const pipelineIds = pipelines.map((p) => p.id);

    if (pipelineIds.length === 0) {
      console.warn("No pipelines found for this location.");
      return;
    }

    // Fetch Opportunities
    const opportunitiesRes = await fetch(
      `${NEW_API_URL}/api/opportunities/search?location_id=${locationId}&pipelineIds=${pipelineIds.join(",")}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${locationAccessToken}`,
        },
      }
    );

    if (!opportunitiesRes.ok) {
      console.error("Failed to fetch opportunities");
      return;
    }

    const data = await opportunitiesRes.json();
    console.log("opportunities response data:", data);

    // Return the data in the format expected by the frontend
    return {
      success: true,
      data: data.data,
      meta: {
        total: data.data?.opportunities?.length || 0
      }
    };

  } catch (error) {
    console.error("Full flow error:", error);
    throw error;
  }
}


async function getLocationsKey(id, ghlApiKey) {
  try {
    const response = await axios.get(`${base_url}/api/locations/${id}/api-key`);
    return response.data;
  } catch (error) {
    console.error("Error fetching location API key:", error);
    return null;
  }
}

const fetchOpportunities = async (locationId, pipelineIds, locationAccessToken) => {
  try {
    const opportunitiesRes = await fetch(
      `${NEW_API_URL}/api/opportunities/search?location_id=${locationId}&pipelineIds=${pipelineIds.join(",")}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${locationAccessToken}`,
        },
      }
    );

    if (!opportunitiesRes.ok) {
      throw new Error('Failed to fetch opportunities');
    }

    const response = await opportunitiesRes.json();

    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch opportunities');
    }

    // Now you can use the data which will be organized by pipeline and stages
    return response.data;
  } catch (error) {
    console.error('Error fetching opportunities:', error);
    throw error;
  }
};

// Example of how to use the data in your component:
const displayOpportunities = (data) => {
  data.forEach(pipeline => {
    console.log(`Pipeline: ${pipeline.pipelineName}`);

    pipeline.stages.forEach(stage => {
      console.log(`Stage: ${stage.stageName}`);
      console.log(`Number of opportunities: ${stage.opportunities.length}`);

      stage.opportunities.forEach(opportunity => {
        console.log(`- ${opportunity.name}`);
      });
    });
  });
};

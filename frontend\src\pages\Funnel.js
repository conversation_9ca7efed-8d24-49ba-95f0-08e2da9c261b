import React, { useState, useEffect } from 'react';
import axios from 'axios';

// You can adjust this to match your backend API URL
const API_BASE_URL = 'http://localhost:5000/api';
const NEW_API_URL = process.env.REACT_APP_API_BASE_URL;

const FunnelDashboard = () => {
  // State for subscription list and pagination
  const [subscriptions, setSubscriptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  
  // State for selected subscription details
  const [selectedSubscription, setSelectedSubscription] = useState(null);
  
  // State for create/update form
  const [formData, setFormData] = useState({
    customerId: '',
    planId: '',
    frequency: 'monthly',
    amount: '',
    startDate: '',
    // Add other fields as needed
  });
  const [formMode, setFormMode] = useState('create'); // 'create' or 'update'
  
  // Fetch all subscriptions
  const fetchSubscriptions = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.get(`${NEW_API_URL}/subscriptions`, {
        params: { page, limit }
      });
      setSubscriptions(response.data.subscriptions || response.data);
    } catch (err) {
      setError(err.response?.data?.error || err.message || 'Failed to fetch subscriptions');
      console.error('Error fetching subscriptions:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch a specific subscription by ID
  const fetchSubscriptionById = async (subscriptionId) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.get(`${NEW_API_URL}/subscriptions/${subscriptionId}`);
      setSelectedSubscription(response.data);
      // Also populate form with current subscription data for editing
      setFormData({
        customerId: response.data.customerId || '',
        planId: response.data.planId || '',
        frequency: response.data.frequency || 'monthly',
        amount: response.data.amount || '',
        startDate: response.data.startDate || '',
        // Add other fields as needed
      });
      setFormMode('update');
    } catch (err) {
      setError(err.response?.data?.error || err.message || `Failed to fetch subscription ${subscriptionId}`);
      console.error(`Error fetching subscription ${subscriptionId}:`, err);
    } finally {
      setLoading(false);
    }
  };
  
  // Create a new subscription
  const createSubscription = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.post(`${NEW_API_URL}/subscriptions`, formData);
      // Reset the form
      setFormData({
        customerId: '',
        planId: '',
        frequency: 'monthly',
        amount: '',
        startDate: '',
      });
      // Refresh the subscription list
      fetchSubscriptions();
      alert('Subscription created successfully!');
    } catch (err) {
      setError(err.response?.data?.error || err.message || 'Failed to create subscription');
      console.error('Error creating subscription:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Update an existing subscription
  const updateSubscription = async (e) => {
    e.preventDefault();
    if (!selectedSubscription?.id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.put(
        `${NEW_API_URL}/subscriptions/${selectedSubscription.id}`, 
        formData
      );
      // Refresh subscription details
      setSelectedSubscription(response.data);
      // Refresh the subscription list
      fetchSubscriptions();
      alert('Subscription updated successfully!');
    } catch (err) {
      setError(err.response?.data?.error || err.message || 'Failed to update subscription');
      console.error(`Error updating subscription ${selectedSubscription.id}:`, err);
    } finally {
      setLoading(false);
    }
  };
  
  // Cancel a subscription
  const cancelSubscription = async (subscriptionId) => {
    if (!window.confirm('Are you sure you want to cancel this subscription?')) return;
    
    setLoading(true);
    setError(null);
    
    try {
      await axios.delete(`${NEW_API_URL}/subscriptions/${subscriptionId}`);
      // If the canceled subscription is currently selected, clear the selection
      if (selectedSubscription?.id === subscriptionId) {
        setSelectedSubscription(null);
        setFormMode('create');
        setFormData({
          customerId: '',
          planId: '',
          frequency: 'monthly',
          amount: '',
          startDate: '',
        });
      }
      // Refresh the subscription list
      fetchSubscriptions();
      alert('Subscription canceled successfully!');
    } catch (err) {
      setError(err.response?.data?.error || err.message || 'Failed to cancel subscription');
      console.error(`Error canceling subscription ${subscriptionId}:`, err);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [name]: value
    }));
  };
  
  // Initialize - fetch subscriptions on component mount
  useEffect(() => {
    fetchSubscriptions();
  }, [page, limit]);
  
  return (
    <div className="subscription-manager">
      <h1>GHL Subscription Manager</h1>
      
      {error && (
        <div className="error-message">
          <p>Error: {error}</p>
          <button onClick={() => setError(null)}>Dismiss</button>
        </div>
      )}
      
      <div className="subscription-container">
        <div className="subscription-list">
          <h2>Subscriptions</h2>
          {loading && <p>Loading...</p>}
          
          {!loading && subscriptions.length === 0 ? (
            <p>No subscriptions found.</p>
          ) : (
            <>
              <table>
                <thead>
                  <tr>
                    <th>Customer</th>
                    <th>Plan</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {subscriptions.map(subscription => (
                    <tr key={subscription.id}>
                      <td>{subscription.customerName || subscription.customerId}</td>
                      <td>{subscription.planName || subscription.planId}</td>
                      <td>${subscription.amount}</td>
                      <td>{subscription.status}</td>
                      <td>
                        <button 
                          onClick={() => fetchSubscriptionById(subscription.id)}
                          className="view-btn"
                        >
                          View/Edit
                        </button>
                        <button 
                          onClick={() => cancelSubscription(subscription.id)}
                          className="cancel-btn"
                        >
                          Cancel
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              <div className="pagination">
                <button 
                  onClick={() => setPage(prev => Math.max(prev - 1, 1))}
                  disabled={page === 1}
                >
                  Previous
                </button>
                <span>Page {page}</span>
                <button onClick={() => setPage(prev => prev + 1)}>
                  Next
                </button>
                <select 
                  value={limit}
                  onChange={(e) => {
                    setLimit(Number(e.target.value));
                    setPage(1);
                  }}
                >
                  <option value={5}>5 per page</option>
                  <option value={10}>10 per page</option>
                  <option value={20}>20 per page</option>
                  <option value={50}>50 per page</option>
                </select>
              </div>
            </>
          )}
        </div>
        
        <div className="subscription-form">
          <h2>{formMode === 'create' ? 'Create New Subscription' : 'Update Subscription'}</h2>
          
          <form onSubmit={formMode === 'create' ? createSubscription : updateSubscription}>
            <div className="form-group">
              <label htmlFor="customerId">Customer ID:</label>
              <input
                type="text"
                id="customerId"
                name="customerId"
                value={formData.customerId}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="planId">Plan ID:</label>
              <input
                type="text"
                id="planId"
                name="planId"
                value={formData.planId}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="frequency">Frequency:</label>
              <select
                id="frequency"
                name="frequency"
                value={formData.frequency}
                onChange={handleInputChange}
                required
              >
                <option value="monthly">Monthly</option>
                <option value="quarterly">Quarterly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>
            
            <div className="form-group">
              <label htmlFor="amount">Amount:</label>
              <input
                type="number"
                id="amount"
                name="amount"
                value={formData.amount}
                onChange={handleInputChange}
                step="0.01"
                min="0"
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="startDate">Start Date:</label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                value={formData.startDate}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div className="form-actions">
              <button type="submit" disabled={loading}>
                {formMode === 'create' ? 'Create Subscription' : 'Update Subscription'}
              </button>
              
              {formMode === 'update' && (
                <button
                  type="button"
                  onClick={() => {
                    setSelectedSubscription(null);
                    setFormMode('create');
                    setFormData({
                      customerId: '',
                      planId: '',
                      frequency: 'monthly',
                      amount: '',
                      startDate: '',
                    });
                  }}
                >
                  Cancel Edit
                </button>
              )}
            </div>
          </form>
        </div>
        
        {selectedSubscription && (
          <div className="subscription-details">
            <h2>Subscription Details</h2>
            <pre>{JSON.stringify(selectedSubscription, null, 2)}</pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default FunnelDashboard;
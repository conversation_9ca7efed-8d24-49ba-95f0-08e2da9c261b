import * as React from "react";
import axios from "axios";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import MuiCard from "@mui/material/Card";
import Checkbox from "@mui/material/Checkbox";
import Divider from "@mui/material/Divider";
import FormLabel from "@mui/material/FormLabel";
import FormControl from "@mui/material/FormControl";
import FormControlLabel from "@mui/material/FormControlLabel";
import Link from "@mui/material/Link";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { styled } from "@mui/material/styles";
import gonanologo from "../static/images/svgviewer-output.svg";
import { GoogleIcon, FacebookIcon, SitemarkIcon } from "./CustomIcons";
import ForgotPassword from "../pages/ForgotPassword";
import InputAdornment from "@mui/material/InputAdornment";
import IconButton from "@mui/material/IconButton";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import Snackbar from "@mui/material/Snackbar";
import Alert from "@mui/material/Alert";
import { ToastContainer, toast } from "react-toastify";
import SubAccountModal from "../components/SubAccountModal";

const Card = styled(MuiCard)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  alignSelf: "center",
  width: "100%",
  padding: theme.spacing(4),
  gap: theme.spacing(2),
  boxShadow:
    "hsla(220, 30%, 5%, 0.05) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.05) 0px 15px 35px -5px",
  [theme.breakpoints.up("sm")]: {
    width: "450px",
  },
  ...theme.applyStyles("dark", {
    boxShadow:
      "hsla(220, 30%, 5%, 0.5) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.08) 0px 15px 35px -5px",
  }),
}));

export default function SignInCard() {
  const [emailError, setEmailError] = useState(false);
  const [emailErrorMessage, setEmailErrorMessage] = useState("");
  const [passwordError, setPasswordError] = useState(false);
  const [passwordErrorMessage, setPasswordErrorMessage] = useState("");
  const [open, setOpen] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loginError, setLoginError] = useState("");
  const [userData, setUserData] = useState({ email: "", password: "" });
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState("");
  const [showSubAccountModal, setShowSubAccountModal] = useState(false);
  const navigate = useNavigate();
 
  const base_url = process.env.REACT_APP_API_BASE_URL;

  console.log("base_url", base_url);

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleChange = (e) => {
    setUserData({ ...userData, [e.target.name]: e.target.value });
  };
  const handleSnackbarClose = () => {
    setSuccessMessage("");
  };

  const email = userData.email;
  const password = userData.password;

  const NEW_API_URL = process.env.REACT_APP_NEW_BASE_URL;

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const response = await axios.post(`${NEW_API_URL}/api/login`, {
        email,
        password,
      });

      const { token, ghlData, user, token_type,expires_in,refresh_token,scope,userType,companyId,userId } = response.data;

      // Combine the user data and token into a single object
      const currentUser = {
        id: user.id,
        name: user.name,
        email: user.email,
        token: token,
        userAccessToken: user.userAccessToken,
        userRefreshToken: user.userRefreshToken,
      };

      // Store required items
      localStorage.setItem("JWTUsertoken", token);
      localStorage.setItem("currentUser", JSON.stringify(currentUser));
      localStorage.setItem("userDetails", JSON.stringify(ghlData.user));
      localStorage.setItem("token_type", JSON.stringify(ghlData.token_type));
      localStorage.setItem("expires_in", JSON.stringify(ghlData.expires_in || ghlData.expiresIn));
      localStorage.setItem("refresh_token", JSON.stringify(ghlData.refresh_token));
      localStorage.setItem("refreshToken", JSON.stringify(ghlData.refreshToken));
      localStorage.setItem("scope", JSON.stringify(ghlData.scope));
      localStorage.setItem("userType", JSON.stringify(ghlData.userType));
      localStorage.setItem("companyId", JSON.stringify(ghlData.companyId));
      localStorage.setItem("userId", JSON.stringify(ghlData.userId));

      if (ghlData.access_token || ghlData.accessToken ) {
        localStorage.setItem("ghlAccessToken", ghlData.access_token);
        localStorage.setItem("ghlaccessToken", ghlData.accessToken);
      } else {
        console.warn("GHL access token not available");
        alert("Login successful, but GHL access token could not be retrieved.");
      }

      console.log("Login successful");
      alert("Login successful");
      // setShowSubAccountModal(true);
      navigate('/select-sub-account')
    } catch (error) {
      console.error(
        "Login failed:",
        error.response?.data?.message || error.message
      );
      alert(
        "Login failed: " +
          (error.response?.data?.message || "Something went wrong")
      );
    }
  };

  return (
    <>
      <ToastContainer />
      <Card variant="outlined">
        <Box sx={{ display: { xs: "flex", md: "none" } }}>
          <img
            src={gonanologo}
            alt="gonanoLogo"
            style={{
              objectFit: "cover",
              height: "40px",
              width: "140px",
              marginLeft: "-10px",
            }}
          />
        </Box>
        <Typography
          component="h1"
          variant="h4"
          sx={{ width: "100%", fontSize: "clamp(2rem, 10vw, 2.15rem)" }}
        >
          Sign in
        </Typography>

        {loginError && (
          <Typography color="error" sx={{ mt: 1 }}>
            {loginError}
          </Typography>
        )}

        <Box
          component="form"
          onSubmit={handleSubmit}
          noValidate
          sx={{
            display: "flex",
            flexDirection: "column",
            width: "100%",
            gap: 2,
          }}
        >
          <FormControl>
            <FormLabel htmlFor="email">Email</FormLabel>
            <TextField
              error={emailError}
              helperText={emailErrorMessage}
              value={userData.email}
              onChange={handleChange}
              id="email"
              type="email"
              name="email"
              placeholder="<EMAIL>"
              autoComplete="email"
              required
              fullWidth
              variant="outlined"
              color={emailError ? "error" : "primary"}
            />
          </FormControl>
          <FormControl>
            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <FormLabel htmlFor="password">Password</FormLabel>
              <Link
                component="button"
                type="button"
                onClick={handleClickOpen}
                variant="body2"
                sx={{ alignSelf: "baseline" }}
              >
                Forgot your password?
              </Link>
            </Box>
            <TextField
              error={passwordError}
              helperText={passwordErrorMessage}
              name="password"
              value={userData.password}
              onChange={handleChange}
              placeholder="••••••"
              type={showPassword ? "text" : "password"}
              id="password"
              autoComplete="current-password"
              required
              fullWidth
              variant="outlined"
              color={passwordError ? "error" : "primary"}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton onClick={togglePasswordVisibility} edge="end">
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
                sx: {
                  "& .MuiIconButton-sizeMedium.css-spzvdl-MuiButtonBase-root-MuiIconButton-root":
                    {
                      paddingLeft: "25px",
                      height: "35px",
                      margin: "6px 0px",
                      border: "none",
                    },
                },
              }}
            />
          </FormControl>
          <ForgotPassword open={open} handleClose={handleClose} />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            disabled={isSubmitting}
            sx={{ marginTop: "1rem" }}
          >
            {isSubmitting ? "Signing in..." : "Sign in"}
          </Button>
          <Typography sx={{ textAlign: "center" }}>
            Don&apos;t have an account?{" "}
            <span>
              <Link
                href="/sign-up"
                variant="body2"
                sx={{ alignSelf: "center" }}
              >
                Sign up
              </Link>
            </span>
          </Typography>
        </Box>
        {/* <Divider>or</Divider> */}
        {/* <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          <Button
            fullWidth
            variant="outlined"
            onClick={handleGoogleSignIn}
            startIcon={<GoogleIcon />}
          >
            Sign in with Google
          </Button>
          <Button
            fullWidth
            variant="outlined"
            onClick={handleFacebookSignIn}
            startIcon={<FacebookIcon />}
          >
            Sign in with Facebook
          </Button>
        </Box> */}
      </Card>
      <Snackbar
        open={!!successMessage}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert variant="filled" severity="success" sx={{ width: "100%" }}>
          {successMessage}
        </Alert>
      </Snackbar>
      {showSubAccountModal && <SubAccountModal />}
    </>
  );
}

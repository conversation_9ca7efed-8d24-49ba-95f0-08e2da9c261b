{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^8.15.0", "mysql2": "^3.14.1", "ngrok": "^5.0.0-beta.2", "nodemon": "^3.1.9"}, "devDependencies": {"nodemon": "^3.0.2"}}